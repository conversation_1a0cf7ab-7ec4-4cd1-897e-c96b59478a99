const request = require('supertest');
const app = require('../src/app');

describe('管理员扩展功能测试', () => {
  let adminToken;
  let userToken;
  let testRoleId;
  let testOrderUuid;
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';
  const testEmail = `admin_ext_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 管理员登录
    const adminLoginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: adminEmail,
        password: adminPassword
      });

    if (adminLoginResponse.body.code === 200) {
      adminToken = adminLoginResponse.body.data.token;
    }

    // 创建测试用户
    const userRegisterResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (userRegisterResponse.body.code === 201) {
      userToken = userRegisterResponse.body.data.token;
    }

    // 创建测试订单
    const plansResponse = await request(app)
      .get('/api/v1/plans');

    if (plansResponse.body.data.length > 0) {
      const planId = plansResponse.body.data[0].id;
      
      const orderResponse = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          plan_id: planId
        });

      if (orderResponse.body.code === 201) {
        testOrderUuid = orderResponse.body.data.order_uuid;
      }
    }
  });

  describe('AI角色管理', () => {
    describe('POST /api/v1/admin/ai-roles', () => {
      test('管理员应该能创建新的AI角色', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const roleData = {
          name: `测试角色_${Date.now()}`,
          avatar_url: 'https://example.com/avatar.jpg',
          description: '这是一个测试AI角色',
          details: '详细的角色描述信息',
          system_prompt: '你是一个测试AI助手，请友好地回答用户的问题。'
        };

        const response = await request(app)
          .post('/api/v1/admin/ai-roles')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(roleData)
          .expect(200);

        expect(response.body.code).toBe(201);
        expect(response.body.message).toBe('AI role created successfully');
        expect(response.body.data).toHaveProperty('id');
        expect(response.body.data).toHaveProperty('name', roleData.name);
        expect(response.body.data).toHaveProperty('avatar_url', roleData.avatar_url);
        expect(response.body.data).toHaveProperty('description', roleData.description);
        expect(response.body.data).toHaveProperty('system_prompt', roleData.system_prompt);
        expect(response.body.data).toHaveProperty('is_active', true);

        testRoleId = response.body.data.id;
      });

      test('应该拒绝重复的角色名称', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const roleData = {
          name: '博学的历史学家', // 已存在的角色名称
          system_prompt: '测试提示词'
        };

        const response = await request(app)
          .post('/api/v1/admin/ai-roles')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(roleData)
          .expect(400);

        expect(response.body.code).toBe(40001);
        expect(response.body.message).toBe('角色名称已存在');
      });

      test('应该拒绝缺少必填字段', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const roleData = {
          description: '缺少名称和系统提示词'
        };

        const response = await request(app)
          .post('/api/v1/admin/ai-roles')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(roleData)
          .expect(400);

        expect(response.body.code).toBe(40001);
      });

      test('普通用户应该被拒绝访问', async () => {
        if (!userToken) {
          console.log('跳过测试：用户创建失败');
          return;
        }

        const roleData = {
          name: '测试角色',
          system_prompt: '测试提示词'
        };

        const response = await request(app)
          .post('/api/v1/admin/ai-roles')
          .set('Authorization', `Bearer ${userToken}`)
          .send(roleData)
          .expect(403);

        expect(response.body.code).toBe(40301);
      });
    });

    describe('PUT /api/v1/admin/ai-roles/:id', () => {
      test('管理员应该能更新AI角色', async () => {
        if (!adminToken || !testRoleId) {
          console.log('跳过测试：管理员登录失败或测试角色不存在');
          return;
        }

        const updateData = {
          description: '更新后的角色描述',
          details: '更新后的详细信息'
        };

        const response = await request(app)
          .put(`/api/v1/admin/ai-roles/${testRoleId}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(updateData)
          .expect(200);

        expect(response.body.code).toBe(200);
        expect(response.body.message).toBe('AI role updated successfully');
        expect(response.body.data).toHaveProperty('description', updateData.description);
        expect(response.body.data).toHaveProperty('details', updateData.details);
      });

      test('应该拒绝不存在的角色ID', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const updateData = {
          description: '更新描述'
        };

        const response = await request(app)
          .put('/api/v1/admin/ai-roles/99999')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(updateData)
          .expect(404);

        expect(response.body.code).toBe(40401);
        expect(response.body.message).toBe('AI角色不存在');
      });

      test('应该拒绝无效的角色ID格式', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const updateData = {
          description: '更新描述'
        };

        const response = await request(app)
          .put('/api/v1/admin/ai-roles/invalid')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(updateData)
          .expect(400);

        expect(response.body.code).toBe(40001);
      });
    });

    describe('DELETE /api/v1/admin/ai-roles/:id', () => {
      test('管理员应该能删除AI角色', async () => {
        if (!adminToken || !testRoleId) {
          console.log('跳过测试：管理员登录失败或测试角色不存在');
          return;
        }

        const response = await request(app)
          .delete(`/api/v1/admin/ai-roles/${testRoleId}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.code).toBe(200);
        expect(response.body.message).toBe('AI role deleted successfully');
        expect(response.body.data).toBeNull();
      });

      test('应该拒绝删除不存在的角色', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const response = await request(app)
          .delete('/api/v1/admin/ai-roles/99999')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(404);

        expect(response.body.code).toBe(40401);
        expect(response.body.message).toBe('AI角色不存在');
      });

      test('应该拒绝重复删除', async () => {
        if (!adminToken || !testRoleId) {
          console.log('跳过测试：管理员登录失败或测试角色不存在');
          return;
        }

        const response = await request(app)
          .delete(`/api/v1/admin/ai-roles/${testRoleId}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(400);

        expect(response.body.code).toBe(40001);
        expect(response.body.message).toBe('AI角色已被删除');
      });
    });
  });

  describe('订单管理', () => {
    describe('GET /api/v1/admin/orders', () => {
      test('管理员应该能获取订单列表', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const response = await request(app)
          .get('/api/v1/admin/orders')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.code).toBe(200);
        expect(response.body.message).toBe('Success');
        expect(response.body.data).toHaveProperty('orders');
        expect(response.body.data).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data.orders)).toBe(true);

        // 检查分页信息
        expect(response.body.data.pagination).toHaveProperty('page');
        expect(response.body.data.pagination).toHaveProperty('limit');
        expect(response.body.data.pagination).toHaveProperty('total');
        expect(response.body.data.pagination).toHaveProperty('pages');

        // 检查订单数据结构
        if (response.body.data.orders.length > 0) {
          const order = response.body.data.orders[0];
          expect(order).toHaveProperty('uuid');
          expect(order).toHaveProperty('amount');
          expect(order).toHaveProperty('status');
          expect(order).toHaveProperty('user_uuid');
          expect(order).toHaveProperty('user_email');
          expect(order).toHaveProperty('plan_name');
          expect(order).toHaveProperty('created_at');
        }
      });

      test('应该支持状态筛选', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const response = await request(app)
          .get('/api/v1/admin/orders?status=pending')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.code).toBe(200);
        // 如果有订单，检查状态筛选是否生效
        if (response.body.data.orders.length > 0) {
          response.body.data.orders.forEach(order => {
            expect(order.status).toBe('pending');
          });
        }
      });

      test('应该支持用户搜索', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const response = await request(app)
          .get('/api/v1/admin/orders?user_search=test')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.code).toBe(200);
      });

      test('应该支持分页参数', async () => {
        if (!adminToken) {
          console.log('跳过测试：管理员登录失败');
          return;
        }

        const response = await request(app)
          .get('/api/v1/admin/orders?page=1&limit=5')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.pagination.page).toBe(1);
        expect(response.body.data.pagination.limit).toBe(5);
        expect(response.body.data.orders.length).toBeLessThanOrEqual(5);
      });

      test('普通用户应该被拒绝访问', async () => {
        if (!userToken) {
          console.log('跳过测试：用户创建失败');
          return;
        }

        const response = await request(app)
          .get('/api/v1/admin/orders')
          .set('Authorization', `Bearer ${userToken}`)
          .expect(403);

        expect(response.body.code).toBe(40301);
      });
    });
  });
});
