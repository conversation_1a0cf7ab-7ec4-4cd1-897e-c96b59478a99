const userCenterService = require('../services/userCenterService');

/**
 * 获取当前用户信息
 */
async function getCurrentUser(req, res, next) {
  try {
    const userId = req.user.id;

    const userProfile = await userCenterService.getUserProfile(userId);
    
    res.success(userProfile, 'Success');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    next(error);
  }
}

/**
 * 获取用户的聊天会话列表
 */
async function getUserChatSessions(req, res, next) {
  try {
    const userId = req.user.id;

    const sessions = await userCenterService.getUserChatSessions(userId);
    
    res.success(sessions, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 修改密码
 */
async function changePassword(req, res, next) {
  try {
    const { old_password, new_password } = req.body;
    const userId = req.user.id;

    await userCenterService.changeUserPassword(userId, old_password, new_password);

    res.success(null, 'Password changed successfully');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    if (error.message === 'GUEST_CANNOT_CHANGE_PASSWORD') {
      return res.error('游客用户无法修改密码，请先注册', 40301, 403);
    }
    if (error.message === 'INVALID_OLD_PASSWORD') {
      return res.error('旧密码错误', 40001, 400);
    }
    if (error.message === 'NEW_PASSWORD_SAME_AS_OLD') {
      return res.error('新密码不能与旧密码相同', 40001, 400);
    }
    next(error);
  }
}

/**
 * 删除指定聊天会话
 */
async function deleteChatSession(req, res, next) {
  try {
    const { session_uuid } = req.params;
    const userId = req.user.id;

    await userCenterService.deleteChatSession(userId, session_uuid);

    res.success(null, 'Chat session deleted successfully');
  } catch (error) {
    if (error.message === 'SESSION_NOT_FOUND') {
      return res.error('聊天会话不存在或无权限访问', 40401, 404);
    }
    next(error);
  }
}

/**
 * 删除所有聊天历史记录
 */
async function deleteAllChatHistory(req, res, next) {
  try {
    const userId = req.user.id;

    await userCenterService.deleteAllChatHistory(userId);

    res.success(null, 'All chat history deleted successfully');
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getCurrentUser,
  getUserChatSessions,
  changePassword,
  deleteChatSession,
  deleteAllChatHistory
};
