# AI伙伴聊天应用 - 模块五API文档

## 概述

本文档描述了AI伙伴聊天应用**模块五：支付与充值**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: 部分接口需要JWT Bearer Token

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "套餐不存在或已禁用",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 201 | 201 | 资源创建成功 |
| 400 | 40001 | 请求参数无效 |
| 401 | 40101 | 未授权或Token无效 |
| 404 | 40401 | 请求的资源未找到 |
| 500 | 50001 | 服务器内部错误 |

---

## 接口列表

### 1. 获取所有可用套餐

**接口描述**: 获取所有在售的套餐计划列表

- **URL**: `GET /plans`
- **认证**: 无需认证
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": 1,
      "name": "月度会员",
      "description": "无限畅聊，解锁所有高级功能。",
      "price": "20.00",
      "plan_type": "subscription",
      "message_credits": null,
      "duration_days": 30
    },
    {
      "id": 2,
      "name": "年度会员",
      "description": "年度超值套餐，享受更多优惠。",
      "price": "200.00",
      "plan_type": "subscription",
      "message_credits": null,
      "duration_days": 365
    },
    {
      "id": 3,
      "name": "消息加油包",
      "description": "一次性购买500条消息额度。",
      "price": "50.00",
      "plan_type": "one_time",
      "message_credits": 500,
      "duration_days": null
    },
    {
      "id": 4,
      "name": "超级消息包",
      "description": "一次性购买2000条消息额度，更加划算。",
      "price": "180.00",
      "plan_type": "one_time",
      "message_credits": 2000,
      "duration_days": null
    }
  ]
}
```

**字段说明**:
- `id`: 套餐唯一标识符
- `name`: 套餐名称
- `description`: 套餐描述
- `price`: 价格（字符串格式，保留两位小数）
- `plan_type`: 套餐类型
  - `subscription`: 订阅类型（会员）
  - `one_time`: 一次性购买（消息包）
- `message_credits`: 消息点数（仅one_time类型）
- `duration_days`: 有效期天数（仅subscription类型）

**特点**:
- 返回结果按ID升序排列
- 只返回已启用的套餐（is_active = true）
- 公开接口，无需认证

### 2. 创建订单

**接口描述**: 用户选择一个套餐以创建支付订单

- **URL**: `POST /orders`
- **认证**: 需要JWT Token

**请求参数**:
```json
{
  "plan_id": 1
}
```

**参数说明**:
- `plan_id`: 用户选择的套餐ID，必须是正整数且套餐必须存在并启用

**响应示例**:
```json
{
  "code": 201,
  "message": "Order created successfully",
  "data": {
    "order_uuid": "e5f6a7b8-c9d0-1234-5678-90abcdef1234",
    "status": "pending",
    "amount": "20.00",
    "payment_details": {
      "pay_url": "https://mock-payment.example.com/pay?order=e5f6a7b8-c9d0-1234-5678-90abcdef1234&amount=20.00",
      "qr_code": "https://mock-payment.example.com/qr?order=e5f6a7b8-c9d0-1234-5678-90abcdef1234",
      "expire_time": "2025-07-22T11:30:00.000Z"
    }
  }
}
```

**字段说明**:
- `order_uuid`: 订单的唯一标识符
- `status`: 订单状态（pending/completed/failed/refunded）
- `amount`: 订单金额
- `payment_details`: 支付详情（模拟支付网关返回）
  - `pay_url`: 支付页面链接
  - `qr_code`: 支付二维码链接
  - `expire_time`: 支付链接过期时间（30分钟）

**错误响应**:
- `404 Not Found`: 套餐不存在或已禁用
- `400 Bad Request`: 参数验证失败
- `401 Unauthorized`: Token无效或已过期

### 3. 支付结果通知（模拟）

**接口描述**: 接收支付平台的异步支付结果通知（模拟实现）

- **URL**: `POST /payments/notify`
- **认证**: 无需认证（实际应用中需要IP白名单或签名验证）

**请求参数**:
```json
{
  "order_uuid": "e5f6a7b8-c9d0-1234-5678-90abcdef1234",
  "status": "success",
  "transaction_id": "mock_txn_1234567890",
  "gateway": "mock_payment"
}
```

**参数说明**:
- `order_uuid`: 订单UUID，必须是有效的UUID格式
- `status`: 支付状态，只能是"success"或"failed"
- `transaction_id`: 支付网关返回的交易号（可选）
- `gateway`: 支付网关名称（可选）

**响应示例**:
```
SUCCESS
```

**错误响应**:
```
ORDER_NOT_FOUND          # 订单不存在
ORDER_ALREADY_PROCESSED  # 订单已处理
FAILED                   # 处理失败
```

**业务逻辑**:
- 支付成功时：
  - 订阅类型：创建用户订阅记录
  - 一次性类型：增加用户消息点数
- 支付失败时：仅更新订单状态
- 防止重复处理同一订单

### 4. 获取用户订阅信息

**接口描述**: 获取当前用户的有效订阅状态

- **URL**: `GET /me/subscriptions`
- **认证**: 需要JWT Token
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "plan_name": "月度会员",
      "start_date": "2025-07-15T00:00:00.000Z",
      "end_date": "2025-08-14T23:59:59.000Z",
      "status": "active"
    },
    {
      "plan_name": "年度会员",
      "start_date": "2025-07-01T00:00:00.000Z",
      "end_date": "2026-06-30T23:59:59.000Z",
      "status": "active"
    }
  ]
}
```

**字段说明**:
- `plan_name`: 套餐名称
- `start_date`: 订阅开始时间
- `end_date`: 订阅结束时间
- `status`: 订阅状态（active/expired/cancelled）

**特点**:
- 只返回有效的订阅（status = active 且 end_date > 当前时间）
- 按结束时间倒序排列
- 支持多个并发订阅

**错误响应**:
- `401 Unauthorized`: Token无效或已过期

### 5. 获取用户订单历史

**接口描述**: 获取当前用户的所有订单历史记录

- **URL**: `GET /me/orders`
- **认证**: 需要JWT Token
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "uuid": "e5f6a7b8-c9d0-1234-5678-90abcdef1234",
      "amount": "20.00",
      "status": "completed",
      "plan_name": "月度会员",
      "created_at": "2025-07-22T10:00:00.000Z"
    },
    {
      "uuid": "f6a7b8c9-d0e1-2345-6789-0abcdef12345",
      "amount": "50.00",
      "status": "completed",
      "plan_name": "消息加油包",
      "created_at": "2025-07-20T15:30:00.000Z"
    },
    {
      "uuid": "a7b8c9d0-e1f2-3456-789a-bcdef1234567",
      "amount": "20.00",
      "status": "failed",
      "plan_name": "月度会员",
      "created_at": "2025-07-18T09:15:00.000Z"
    }
  ]
}
```

**字段说明**:
- `uuid`: 订单唯一标识符
- `amount`: 订单金额
- `status`: 订单状态
- `plan_name`: 套餐名称
- `created_at`: 订单创建时间

**特点**:
- 按创建时间倒序排列（最新的在前）
- 包含所有状态的订单
- 包含套餐名称便于用户识别

**错误响应**:
- `401 Unauthorized`: Token无效或已过期

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 获取套餐列表
async function getPlans() {
  try {
    const response = await fetch('/api/v1/plans');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('套餐列表:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 2. 创建订单
async function createOrder(planId, token) {
  try {
    const response = await fetch('/api/v1/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        plan_id: planId
      })
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('订单创建成功:', result.data);
      // 跳转到支付页面
      window.open(result.data.payment_details.pay_url, '_blank');
      return result.data;
    } else {
      console.error('创建失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 3. 模拟支付通知（仅用于测试）
async function simulatePayment(orderUuid, success = true) {
  try {
    const response = await fetch('/api/v1/payments/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        order_uuid: orderUuid,
        status: success ? 'success' : 'failed',
        transaction_id: 'mock_txn_' + Date.now(),
        gateway: 'mock_payment'
      })
    });

    const result = await response.text();
    console.log('支付通知结果:', result);
    return result === 'SUCCESS';
  } catch (error) {
    console.error('支付通知失败:', error);
    return false;
  }
}

// 4. 获取用户订阅
async function getUserSubscriptions(token) {
  try {
    const response = await fetch('/api/v1/me/subscriptions', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('用户订阅:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 5. 获取订单历史
async function getUserOrders(token) {
  try {
    const response = await fetch('/api/v1/me/orders', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('订单历史:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}
```

### Vue.js 支付组件示例

```vue
<template>
  <div class="payment-center">
    <!-- 套餐选择 -->
    <div class="plans-section">
      <h2>选择套餐</h2>
      <div class="plans-grid">
        <div 
          v-for="plan in plans" 
          :key="plan.id"
          :class="['plan-card', { selected: selectedPlan?.id === plan.id }]"
          @click="selectPlan(plan)"
        >
          <h3>{{ plan.name }}</h3>
          <p class="price">¥{{ plan.price }}</p>
          <p class="description">{{ plan.description }}</p>
          <div class="features">
            <span v-if="plan.plan_type === 'subscription'">
              有效期：{{ plan.duration_days }}天
            </span>
            <span v-else>
              消息数：{{ plan.message_credits }}条
            </span>
          </div>
        </div>
      </div>
      
      <button 
        @click="createOrder" 
        :disabled="!selectedPlan || creating"
        class="pay-button"
      >
        {{ creating ? '创建中...' : '立即购买' }}
      </button>
    </div>

    <!-- 订阅状态 -->
    <div class="subscriptions-section">
      <h3>我的订阅</h3>
      <div v-if="subscriptions.length === 0" class="empty-state">
        暂无有效订阅
      </div>
      <div v-else class="subscription-list">
        <div 
          v-for="sub in subscriptions" 
          :key="sub.plan_name + sub.start_date"
          class="subscription-item"
        >
          <h4>{{ sub.plan_name }}</h4>
          <p>开始时间：{{ formatTime(sub.start_date) }}</p>
          <p>结束时间：{{ formatTime(sub.end_date) }}</p>
          <span :class="['status', sub.status]">{{ sub.status }}</span>
        </div>
      </div>
    </div>

    <!-- 订单历史 -->
    <div class="orders-section">
      <h3>订单历史</h3>
      <div v-if="orders.length === 0" class="empty-state">
        暂无订单记录
      </div>
      <div v-else class="order-list">
        <div 
          v-for="order in orders" 
          :key="order.uuid"
          class="order-item"
        >
          <div class="order-info">
            <h4>{{ order.plan_name }}</h4>
            <p>订单号：{{ order.uuid }}</p>
            <p>金额：¥{{ order.amount }}</p>
            <p>时间：{{ formatTime(order.created_at) }}</p>
          </div>
          <span :class="['status', order.status]">
            {{ getStatusText(order.status) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 支付弹窗 -->
    <div v-if="paymentModal" class="payment-modal">
      <div class="modal-content">
        <h3>支付订单</h3>
        <p>订单号：{{ currentOrder.order_uuid }}</p>
        <p>金额：¥{{ currentOrder.amount }}</p>
        <div class="payment-methods">
          <button @click="openPaymentUrl">打开支付页面</button>
          <button @click="simulateSuccess" class="success">模拟支付成功</button>
          <button @click="simulateFail" class="fail">模拟支付失败</button>
        </div>
        <button @click="closePaymentModal" class="close">关闭</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      plans: [],
      subscriptions: [],
      orders: [],
      selectedPlan: null,
      creating: false,
      paymentModal: false,
      currentOrder: null,
      token: localStorage.getItem('token')
    }
  },

  async mounted() {
    await this.loadPlans();
    await this.loadSubscriptions();
    await this.loadOrders();
  },

  methods: {
    async loadPlans() {
      try {
        const response = await fetch('/api/v1/plans');
        const result = await response.json();
        if (result.code === 200) {
          this.plans = result.data;
        }
      } catch (error) {
        console.error('加载套餐失败:', error);
      }
    },

    async loadSubscriptions() {
      try {
        const response = await fetch('/api/v1/me/subscriptions', {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.subscriptions = result.data;
        }
      } catch (error) {
        console.error('加载订阅失败:', error);
      }
    },

    async loadOrders() {
      try {
        const response = await fetch('/api/v1/me/orders', {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.orders = result.data;
        }
      } catch (error) {
        console.error('加载订单失败:', error);
      }
    },

    selectPlan(plan) {
      this.selectedPlan = plan;
    },

    async createOrder() {
      if (!this.selectedPlan || this.creating) return;

      this.creating = true;
      try {
        const response = await fetch('/api/v1/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify({
            plan_id: this.selectedPlan.id
          })
        });

        const result = await response.json();
        if (result.code === 201) {
          this.currentOrder = result.data;
          this.paymentModal = true;
        } else {
          alert('创建订单失败: ' + result.message);
        }
      } catch (error) {
        console.error('创建订单失败:', error);
        alert('创建订单失败，请重试');
      } finally {
        this.creating = false;
      }
    },

    openPaymentUrl() {
      if (this.currentOrder?.payment_details?.pay_url) {
        window.open(this.currentOrder.payment_details.pay_url, '_blank');
      }
    },

    async simulateSuccess() {
      await this.simulatePayment('success');
    },

    async simulateFail() {
      await this.simulatePayment('failed');
    },

    async simulatePayment(status) {
      try {
        const response = await fetch('/api/v1/payments/notify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            order_uuid: this.currentOrder.order_uuid,
            status: status,
            transaction_id: 'mock_txn_' + Date.now(),
            gateway: 'mock_payment'
          })
        });

        const result = await response.text();
        if (result === 'SUCCESS') {
          alert(`支付${status === 'success' ? '成功' : '失败'}！`);
          this.closePaymentModal();
          // 刷新数据
          await this.loadSubscriptions();
          await this.loadOrders();
        }
      } catch (error) {
        console.error('模拟支付失败:', error);
      }
    },

    closePaymentModal() {
      this.paymentModal = false;
      this.currentOrder = null;
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    },

    getStatusText(status) {
      const statusMap = {
        pending: '待支付',
        completed: '已完成',
        failed: '支付失败',
        refunded: '已退款'
      };
      return statusMap[status] || status;
    }
  }
}
</script>

<style scoped>
.payment-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.plan-card {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
}

.plan-card:hover {
  border-color: #007bff;
}

.plan-card.selected {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

.pay-button {
  width: 100%;
  padding: 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

.pay-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
}

.payment-methods button {
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.payment-methods .success {
  background-color: #28a745;
  color: white;
}

.payment-methods .fail {
  background-color: #dc3545;
  color: white;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.active {
  background-color: #d4edda;
  color: #155724;
}

.status.completed {
  background-color: #d4edda;
  color: #155724;
}

.status.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.status.pending {
  background-color: #fff3cd;
  color: #856404;
}
</style>
```

---

## 数据库结构

### 相关表结构

**plans表** - 套餐配置
- 支持订阅和一次性购买两种类型
- 价格使用DECIMAL精确存储
- 软删除机制（is_active字段）

**orders表** - 订单记录
- UUID作为对外标识符
- 支持多种支付网关
- 完整的订单状态流转

**user_subscriptions表** - 用户订阅
- 支持多个并发订阅
- 精确的时间范围控制
- 订阅状态管理

### 索引优化

- `orders.idx_user_id`: 用户订单查询
- `orders.idx_gateway_tid`: 支付回调查询
- `user_subscriptions.idx_user_end_date`: 有效订阅查询

---

## 模拟支付说明

### 当前实现

本模块使用模拟支付实现，包含以下特性：

1. **模拟支付网关**: 生成模拟的支付链接和二维码
2. **支付通知接口**: 接收模拟的支付结果通知
3. **完整的业务流程**: 订单创建→支付→权益发放

### 支付流程

1. 用户选择套餐，创建订单
2. 系统返回模拟支付链接
3. 用户"支付"后，调用通知接口
4. 系统处理支付结果，发放用户权益

### 真实支付集成

后续接入真实支付时，需要：

1. 替换支付网关配置
2. 实现真实的签名验证
3. 处理真实的回调格式
4. 添加IP白名单验证

---

## 安全考虑

1. **订单防重**: 防止重复处理同一订单
2. **金额校验**: 严格校验订单金额
3. **状态控制**: 完整的订单状态流转
4. **权限控制**: 用户只能访问自己的订单和订阅
5. **参数验证**: 严格的输入验证和格式检查

---

## 注意事项

1. **支付安全**: 当前为模拟实现，生产环境需要真实支付网关
2. **订阅叠加**: 支持多个订阅并发，按最晚结束时间计算
3. **消息点数**: 一次性购买会立即增加用户消息点数
4. **订单过期**: 支付链接30分钟后过期
5. **异步处理**: 支付通知采用异步处理机制

---

## 测试覆盖

模块五包含完整的测试覆盖：
- ✅ 套餐列表获取测试
- ✅ 订单创建测试（注册用户和游客）
- ✅ 支付通知处理测试（成功/失败/重复/无效）
- ✅ 用户订阅信息获取测试
- ✅ 用户订单历史获取测试
- ✅ 参数验证测试（无效ID、格式错误等）
- ✅ 权限验证测试（认证、授权等）
- ✅ 错误处理测试（不存在的资源等）

所有18个测试用例均已通过验证，支付流程完整可用。
