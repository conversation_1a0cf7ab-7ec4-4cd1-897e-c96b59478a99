const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');

describe('聊天删除功能测试', () => {
  let userToken;
  let sessionUuid1, sessionUuid2;
  const testEmail = `chat_delete_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 创建测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;

    // 获取AI角色
    const rolesResponse = await request(app)
      .get('/api/v1/ai-roles');

    const validRoleId = rolesResponse.body.data[0].id;

    // 创建两个测试会话
    const session1Response = await request(app)
      .post('/api/v1/chat/sessions')
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        ai_role_id: validRoleId
      });

    sessionUuid1 = session1Response.body.data.uuid;

    const session2Response = await request(app)
      .post('/api/v1/chat/sessions')
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        ai_role_id: validRoleId
      });

    sessionUuid2 = session2Response.body.data.uuid;

    // 在会话中发送一些测试消息
    await request(app)
      .post(`/api/v1/chat/sessions/${sessionUuid1}/messages`)
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        content: '测试消息1'
      });

    await request(app)
      .post(`/api/v1/chat/sessions/${sessionUuid2}/messages`)
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        content: '测试消息2'
      });

    // 等待AI回复
    await new Promise(resolve => setTimeout(resolve, 2000));
  });

  describe('DELETE /api/v1/chat/sessions/:session_uuid/messages', () => {
    test('用户应该能清空会话的消息历史', async () => {
      // 获取清空前的消息数量
      const beforeMessages = await query(
        'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
        [sessionUuid1]
      );
      
      expect(beforeMessages[0].count).toBeGreaterThan(0);

      // 清空消息历史
      const response = await request(app)
        .delete(`/api/v1/chat/sessions/${sessionUuid1}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Session messages cleared successfully');

      // 验证消息已被清空
      const afterMessages = await query(
        'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
        [sessionUuid1]
      );
      
      expect(afterMessages[0].count).toBe(0);

      // 验证会话仍然存在
      const sessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE uuid = ?',
        [sessionUuid1]
      );
      
      expect(sessions[0].count).toBe(1);
    });

    test('应该拒绝清空不存在的会话', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      
      const response = await request(app)
        .delete(`/api/v1/chat/sessions/${fakeUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('聊天会话不存在或无权访问');
    });

    test('应该拒绝无效的UUID格式', async () => {
      const response = await request(app)
        .delete('/api/v1/chat/sessions/invalid-uuid/messages')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('DELETE /api/v1/chat/sessions/:session_uuid', () => {
    test('用户应该能删除整个聊天会话', async () => {
      // 验证会话存在
      const beforeSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE uuid = ?',
        [sessionUuid2]
      );
      
      expect(beforeSessions[0].count).toBe(1);

      // 删除会话
      const response = await request(app)
        .delete(`/api/v1/chat/sessions/${sessionUuid2}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Chat session deleted successfully');

      // 验证会话已被删除
      const afterSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE uuid = ?',
        [sessionUuid2]
      );
      
      expect(afterSessions[0].count).toBe(0);

      // 验证相关消息也被删除
      const messages = await query(
        'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
        [sessionUuid2]
      );
      
      expect(messages[0].count).toBe(0);
    });

    test('应该拒绝删除不存在的会话', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      
      const response = await request(app)
        .delete(`/api/v1/chat/sessions/${fakeUuid}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('聊天会话不存在或无权访问');
    });
  });

  describe('DELETE /api/v1/chat/sessions', () => {
    test('用户应该能删除所有聊天记录', async () => {
      // 创建新的测试会话
      const rolesResponse = await request(app)
        .get('/api/v1/ai-roles');

      const validRoleId = rolesResponse.body.data[0].id;

      await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: validRoleId
        });

      // 验证用户有会话
      const beforeSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE user_id = (SELECT id FROM users WHERE uuid = ?)',
        [userToken.split('.')[1]] // 这里简化处理，实际应该解析JWT
      );

      // 删除所有聊天记录
      const response = await request(app)
        .delete('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('All chat records deleted successfully');

      // 验证用户的会话列表为空
      const sessionsResponse = await request(app)
        .get('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(sessionsResponse.body.data).toEqual([]);
    });
  });

  describe('权限验证', () => {
    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .delete(`/api/v1/chat/sessions/${sessionUuid1}`)
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });
});
