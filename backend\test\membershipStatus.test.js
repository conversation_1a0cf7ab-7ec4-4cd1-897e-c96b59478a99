const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');

describe('用户会员状态测试', () => {
  let userToken;
  let userId;
  let planId;
  const testEmail = `membership_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 注册测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (registerResponse.body.code === 201) {
      userToken = registerResponse.body.data.token;
      
      // 获取用户ID
      const users = await query(
        'SELECT id FROM users WHERE email = ?',
        [testEmail]
      );
      userId = users[0].id;
    }

    // 获取一个订阅套餐
    const plansResponse = await request(app)
      .get('/api/v1/plans');

    if (plansResponse.body.data && plansResponse.body.data.length > 0) {
      const subscriptionPlan = plansResponse.body.data.find(plan => plan.plan_type === 'subscription');
      if (subscriptionPlan) {
        planId = subscriptionPlan.id;
      }
    }
  });

  describe('用户个人信息中的会员状态', () => {
    test('普通用户的个人信息应该显示非会员状态', async () => {
      if (!userToken) {
        console.log('跳过测试：用户注册失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/user/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('is_vip', false);
      expect(response.body.data).toHaveProperty('subscription_info', null);
      expect(response.body.data).toHaveProperty('email', testEmail);
    });

    test('用户统计信息应该包含会员状态', async () => {
      if (!userToken) {
        console.log('跳过测试：用户注册失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/user/stats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('is_vip', false);
      expect(response.body.data).toHaveProperty('total_sessions');
      expect(response.body.data).toHaveProperty('total_messages');
      expect(response.body.data).toHaveProperty('total_orders');
      expect(response.body.data).toHaveProperty('total_spent');
      expect(response.body.data).toHaveProperty('daily_message_count');
      expect(response.body.data).toHaveProperty('message_credits');
      expect(response.body.data).toHaveProperty('member_since');
    });
  });

  describe('用户购买订阅后的会员状态', () => {
    test('用户购买订阅套餐后应该变成会员', async () => {
      if (!userToken || !planId) {
        console.log('跳过测试：用户注册失败或没有订阅套餐');
        return;
      }

      // 创建订单
      const orderResponse = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          plan_id: planId
        });

      if (orderResponse.body.code !== 201) {
        console.log('跳过测试：订单创建失败');
        return;
      }

      const orderUuid = orderResponse.body.data.order_uuid;

      // 模拟支付成功
      const paymentResponse = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: orderUuid,
          status: 'success',
          transaction_id: `txn_${Date.now()}`
        });

      expect(paymentResponse.status).toBe(200);

      // 等待一下确保数据库更新完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 检查用户个人信息中的会员状态
      const profileResponse = await request(app)
        .get('/api/v1/user/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(profileResponse.body.code).toBe(200);
      expect(profileResponse.body.data).toHaveProperty('is_vip', true);
      expect(profileResponse.body.data).toHaveProperty('subscription_info');
      expect(profileResponse.body.data.subscription_info).not.toBeNull();
      expect(profileResponse.body.data.subscription_info).toHaveProperty('plan_name');
      expect(profileResponse.body.data.subscription_info).toHaveProperty('start_date');
      expect(profileResponse.body.data.subscription_info).toHaveProperty('end_date');
      expect(profileResponse.body.data.subscription_info).toHaveProperty('status', 'active');
    });

    test('会员用户的统计信息应该显示会员状态', async () => {
      if (!userToken) {
        console.log('跳过测试：用户注册失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/user/stats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('is_vip', true);
      expect(response.body.data).toHaveProperty('total_orders');
      expect(response.body.data.total_orders).toBeGreaterThan(0);
      expect(response.body.data).toHaveProperty('total_spent');
      expect(response.body.data.total_spent).toBeGreaterThan(0);
    });
  });

  describe('数据库一致性检查', () => {
    test('数据库中的订阅记录应该正确', async () => {
      if (!userId) {
        console.log('跳过测试：用户ID未获取');
        return;
      }

      // 检查用户订阅表
      const subscriptions = await query(
        'SELECT us.*, p.name as plan_name FROM user_subscriptions us ' +
        'JOIN plans p ON us.plan_id = p.id ' +
        'WHERE us.user_id = ? AND us.status = "active"',
        [userId]
      );

      if (subscriptions.length > 0) {
        const subscription = subscriptions[0];
        expect(subscription.status).toBe('active');
        expect(subscription.plan_name).toBeDefined();
        expect(new Date(subscription.end_date)).toBeInstanceOf(Date);
        expect(new Date(subscription.end_date).getTime()).toBeGreaterThan(Date.now());
      }
    });

    test('用户订单状态应该正确', async () => {
      if (!userId) {
        console.log('跳过测试：用户ID未获取');
        return;
      }

      // 检查用户订单
      const orders = await query(
        'SELECT o.*, p.name as plan_name FROM orders o ' +
        'JOIN plans p ON o.plan_id = p.id ' +
        'WHERE o.user_id = ? AND o.status = "completed"',
        [userId]
      );

      if (orders.length > 0) {
        const order = orders[0];
        expect(order.status).toBe('completed');
        expect(order.amount).toBeGreaterThan(0);
        expect(order.plan_name).toBeDefined();
      }
    });
  });

  afterAll(async () => {
    // 清理测试数据
    if (userId) {
      await query('DELETE FROM user_subscriptions WHERE user_id = ?', [userId]);
      await query('DELETE FROM orders WHERE user_id = ?', [userId]);
      await query('DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id = ?)', [userId]);
      await query('DELETE FROM chat_sessions WHERE user_id = ?', [userId]);
      await query('DELETE FROM users WHERE id = ?', [userId]);
    }
  });
});
