const request = require('supertest');
const app = require('../src/app');

describe('套餐管理测试', () => {
  let adminToken;
  let userToken;
  let testPlanId;
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';
  const testEmail = `plan_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 管理员登录
    const adminLoginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: adminEmail,
        password: adminPassword
      });

    if (adminLoginResponse.body.code === 200) {
      adminToken = adminLoginResponse.body.data.token;
    }

    // 创建测试用户
    const userRegisterResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (userRegisterResponse.body.code === 201) {
      userToken = userRegisterResponse.body.data.token;
    }
  });

  describe('POST /api/v1/admin/plans', () => {
    test('管理员应该能创建订阅套餐', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const planData = {
        name: `测试订阅套餐_${Date.now()}`,
        description: '这是一个测试订阅套餐',
        price: 29.99,
        plan_type: 'subscription',
        duration_days: 30
      };

      const response = await request(app)
        .post('/api/v1/admin/plans')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(planData)
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.message).toBe('Plan created successfully');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('name', planData.name);
      expect(response.body.data).toHaveProperty('price', planData.price);
      expect(response.body.data).toHaveProperty('plan_type', planData.plan_type);
      expect(response.body.data).toHaveProperty('duration_days', planData.duration_days);
      expect(response.body.data).toHaveProperty('message_credits', null);
      expect(response.body.data).toHaveProperty('is_active', true);

      testPlanId = response.body.data.id;
    });

    test('管理员应该能创建一次性套餐', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const planData = {
        name: `测试一次性套餐_${Date.now()}`,
        description: '这是一个测试一次性套餐',
        price: 9.99,
        plan_type: 'one_time',
        message_credits: 100
      };

      const response = await request(app)
        .post('/api/v1/admin/plans')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(planData)
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.data).toHaveProperty('plan_type', 'one_time');
      expect(response.body.data).toHaveProperty('message_credits', 100);
      expect(response.body.data).toHaveProperty('duration_days', null);
    });

    test('应该拒绝重复的套餐名称', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const planData = {
        name: '月度会员', // 已存在的套餐名称
        price: 20.00,
        plan_type: 'subscription',
        duration_days: 30
      };

      const response = await request(app)
        .post('/api/v1/admin/plans')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(planData)
        .expect(400);

      expect(response.body.code).toBe(40001);
      expect(response.body.message).toBe('套餐名称已存在');
    });

    test('应该拒绝订阅套餐设置消息点数', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const planData = {
        name: `错误订阅套餐_${Date.now()}`,
        price: 20.00,
        plan_type: 'subscription',
        duration_days: 30,
        message_credits: 100 // 订阅套餐不应该有消息点数
      };

      const response = await request(app)
        .post('/api/v1/admin/plans')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(planData)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝一次性套餐设置有效期', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const planData = {
        name: `错误一次性套餐_${Date.now()}`,
        price: 10.00,
        plan_type: 'one_time',
        message_credits: 100,
        duration_days: 30 // 一次性套餐不应该有有效期
      };

      const response = await request(app)
        .post('/api/v1/admin/plans')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(planData)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('普通用户应该被拒绝访问', async () => {
      if (!userToken) {
        console.log('跳过测试：用户创建失败');
        return;
      }

      const planData = {
        name: '测试套餐',
        price: 10.00,
        plan_type: 'one_time',
        message_credits: 100
      };

      const response = await request(app)
        .post('/api/v1/admin/plans')
        .set('Authorization', `Bearer ${userToken}`)
        .send(planData)
        .expect(403);

      expect(response.body.code).toBe(40301);
    });
  });

  describe('PUT /api/v1/admin/plans/:id', () => {
    test('管理员应该能更新套餐', async () => {
      if (!adminToken || !testPlanId) {
        console.log('跳过测试：管理员登录失败或测试套餐不存在');
        return;
      }

      const updateData = {
        description: '更新后的套餐描述',
        price: 39.99
      };

      const response = await request(app)
        .put(`/api/v1/admin/plans/${testPlanId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Plan updated successfully');
      expect(response.body.data).toHaveProperty('description', updateData.description);
      expect(response.body.data).toHaveProperty('price', updateData.price);
    });

    test('应该拒绝不存在的套餐ID', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const updateData = {
        description: '更新描述'
      };

      const response = await request(app)
        .put('/api/v1/admin/plans/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('套餐不存在');
    });

    test('应该拒绝无效的套餐ID格式', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const updateData = {
        description: '更新描述'
      };

      const response = await request(app)
        .put('/api/v1/admin/plans/invalid')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('DELETE /api/v1/admin/plans/:id', () => {
    test('管理员应该能禁用套餐', async () => {
      if (!adminToken || !testPlanId) {
        console.log('跳过测试：管理员登录失败或测试套餐不存在');
        return;
      }

      const response = await request(app)
        .delete(`/api/v1/admin/plans/${testPlanId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Plan disabled successfully');
      expect(response.body.data).toBeNull();
    });

    test('应该拒绝禁用不存在的套餐', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const response = await request(app)
        .delete('/api/v1/admin/plans/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('套餐不存在');
    });

    test('应该拒绝重复禁用', async () => {
      if (!adminToken || !testPlanId) {
        console.log('跳过测试：管理员登录失败或测试套餐不存在');
        return;
      }

      const response = await request(app)
        .delete(`/api/v1/admin/plans/${testPlanId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      expect(response.body.code).toBe(40001);
      expect(response.body.message).toBe('套餐已被禁用');
    });
  });
});
