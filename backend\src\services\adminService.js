const { query } = require('../config/database');

/**
 * 获取用户列表（分页）
 */
async function getUserList(page = 1, limit = 20, search = '') {
  const offset = (page - 1) * limit;
  
  let whereClause = '';
  let queryParams = [];
  
  if (search) {
    whereClause = 'WHERE email LIKE ? OR uuid LIKE ?';
    queryParams = [`%${search}%`, `%${search}%`];
  }
  
  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;
  
  // 获取用户列表
  const listQuery = `
    SELECT uuid, email, is_guest, role, status, daily_message_count, message_credits, created_at, updated_at
    FROM users
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;
  const users = await query(listQuery, queryParams);
  
  // 转换布尔值
  users.forEach(user => {
    user.is_guest = Boolean(user.is_guest);
  });
  
  return {
    users,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
}

/**
 * 根据UUID获取用户详情
 */
async function getUserByUuid(userUuid) {
  const users = await query(
    'SELECT uuid, email, is_guest, role, status, daily_message_count, message_credits, profile_summary, created_at, updated_at FROM users WHERE uuid = ?',
    [userUuid]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];
  user.is_guest = Boolean(user.is_guest);
  
  return user;
}

/**
 * 更新用户状态
 */
async function updateUserStatus(userUuid, status) {
  // 检查用户是否存在
  const users = await query(
    'SELECT id, role FROM users WHERE uuid = ?',
    [userUuid]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];

  // 防止管理员被禁用
  if (user.role === 'admin' && status === 'banned') {
    throw new Error('CANNOT_BAN_ADMIN');
  }

  // 更新用户状态
  await query(
    'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE uuid = ?',
    [status, userUuid]
  );

  return true;
}

/**
 * 获取系统统计信息
 */
async function getSystemStats() {
  // 用户统计
  const userStats = await query(`
    SELECT 
      COUNT(*) as total_users,
      SUM(CASE WHEN is_guest = 1 THEN 1 ELSE 0 END) as guest_users,
      SUM(CASE WHEN is_guest = 0 THEN 1 ELSE 0 END) as registered_users,
      SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
      SUM(CASE WHEN status = 'banned' THEN 1 ELSE 0 END) as banned_users
    FROM users
  `);

  // 今日新增用户
  const todayUsers = await query(`
    SELECT COUNT(*) as today_new_users 
    FROM users 
    WHERE DATE(created_at) = CURDATE()
  `);

  // 聊天会话统计
  const sessionStats = await query(`
    SELECT 
      COUNT(*) as total_sessions,
      COUNT(DISTINCT user_id) as active_chat_users
    FROM chat_sessions
  `);

  // 今日聊天会话
  const todaySessions = await query(`
    SELECT COUNT(*) as today_sessions 
    FROM chat_sessions 
    WHERE DATE(created_at) = CURDATE()
  `);

  // 消息统计
  const messageStats = await query(`
    SELECT 
      COUNT(*) as total_messages,
      SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as user_messages,
      SUM(CASE WHEN role = 'assistant' THEN 1 ELSE 0 END) as ai_messages
    FROM chat_messages
  `);

  // 今日消息
  const todayMessages = await query(`
    SELECT COUNT(*) as today_messages 
    FROM chat_messages 
    WHERE DATE(created_at) = CURDATE()
  `);

  // 订单统计
  const orderStats = await query(`
    SELECT 
      COUNT(*) as total_orders,
      SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
      SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue
    FROM orders
  `);

  // 今日订单
  const todayOrders = await query(`
    SELECT 
      COUNT(*) as today_orders,
      SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as today_revenue
    FROM orders 
    WHERE DATE(created_at) = CURDATE()
  `);

  return {
    users: {
      total: userStats[0].total_users,
      guest: userStats[0].guest_users,
      registered: userStats[0].registered_users,
      active: userStats[0].active_users,
      banned: userStats[0].banned_users,
      today_new: todayUsers[0].today_new_users
    },
    chats: {
      total_sessions: sessionStats[0].total_sessions,
      active_chat_users: sessionStats[0].active_chat_users,
      today_sessions: todaySessions[0].today_sessions,
      total_messages: messageStats[0].total_messages,
      user_messages: messageStats[0].user_messages,
      ai_messages: messageStats[0].ai_messages,
      today_messages: todayMessages[0].today_messages
    },
    orders: {
      total: orderStats[0].total_orders,
      completed: orderStats[0].completed_orders,
      total_revenue: parseFloat(orderStats[0].total_revenue || 0),
      today_orders: todayOrders[0].today_orders,
      today_revenue: parseFloat(todayOrders[0].today_revenue || 0)
    }
  };
}

/**
 * 获取用户的聊天会话列表
 */
async function getUserChatSessions(userUuid) {
  // 先获取用户ID
  const users = await query('SELECT id FROM users WHERE uuid = ?', [userUuid]);
  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const userId = users[0].id;

  const sessions = await query(
    'SELECT cs.uuid, cs.title, cs.created_at, cs.updated_at, ar.name as ai_role_name ' +
    'FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.user_id = ? ' +
    'ORDER BY cs.updated_at DESC',
    [userId]
  );

  return sessions;
}

/**
 * 获取用户的订单列表
 */
async function getUserOrders(userUuid) {
  // 先获取用户ID
  const users = await query('SELECT id FROM users WHERE uuid = ?', [userUuid]);
  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const userId = users[0].id;

  const orders = await query(
    'SELECT o.uuid, o.amount, o.status, o.created_at, p.name as plan_name ' +
    'FROM orders o ' +
    'JOIN plans p ON o.plan_id = p.id ' +
    'WHERE o.user_id = ? ' +
    'ORDER BY o.created_at DESC',
    [userId]
  );

  return orders;
}

/**
 * 创建AI角色
 */
async function createAiRole(roleData) {
  const { name, avatar_url, description, details, system_prompt } = roleData;

  // 检查角色名称是否已存在
  const existingRoles = await query(
    'SELECT COUNT(*) as count FROM ai_roles WHERE name = ? AND is_active = 1',
    [name]
  );

  if (existingRoles[0].count > 0) {
    throw new Error('ROLE_NAME_EXISTS');
  }

  // 创建新角色
  const result = await query(
    'INSERT INTO ai_roles (name, avatar_url, description, details, system_prompt, is_active) VALUES (?, ?, ?, ?, ?, ?)',
    [name, avatar_url || null, description || null, details || null, system_prompt, true]
  );

  return {
    id: result.insertId,
    name,
    avatar_url: avatar_url || null,
    description: description || null,
    details: details || null,
    system_prompt,
    is_active: true
  };
}

/**
 * 更新AI角色
 */
async function updateAiRole(roleId, updateData) {
  // 检查角色是否存在
  const roles = await query(
    'SELECT id, name FROM ai_roles WHERE id = ?',
    [roleId]
  );

  if (roles.length === 0) {
    throw new Error('ROLE_NOT_FOUND');
  }

  // 如果更新名称，检查是否与其他角色重复
  if (updateData.name && updateData.name !== roles[0].name) {
    const existingRoles = await query(
      'SELECT COUNT(*) as count FROM ai_roles WHERE name = ? AND id != ? AND is_active = 1',
      [updateData.name, roleId]
    );

    if (existingRoles[0].count > 0) {
      throw new Error('ROLE_NAME_EXISTS');
    }
  }

  // 构建更新字段
  const updateFields = [];
  const updateValues = [];

  Object.keys(updateData).forEach(key => {
    if (updateData[key] !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(updateData[key]);
    }
  });

  if (updateFields.length === 0) {
    throw new Error('NO_UPDATE_DATA');
  }

  updateFields.push('updated_at = CURRENT_TIMESTAMP');
  updateValues.push(roleId);

  // 执行更新
  await query(
    `UPDATE ai_roles SET ${updateFields.join(', ')} WHERE id = ?`,
    updateValues
  );

  // 返回更新后的角色信息
  const updatedRoles = await query(
    'SELECT id, name, avatar_url, description, details, system_prompt, is_active, created_at, updated_at FROM ai_roles WHERE id = ?',
    [roleId]
  );

  return updatedRoles[0];
}

/**
 * 删除AI角色（软删除）
 */
async function deleteAiRole(roleId) {
  // 检查角色是否存在
  const roles = await query(
    'SELECT id, is_active FROM ai_roles WHERE id = ?',
    [roleId]
  );

  if (roles.length === 0) {
    throw new Error('ROLE_NOT_FOUND');
  }

  if (!roles[0].is_active) {
    throw new Error('ROLE_ALREADY_DELETED');
  }

  // 检查是否有正在使用的会话
  const activeSessions = await query(
    'SELECT COUNT(*) as count FROM chat_sessions WHERE ai_role_id = ?',
    [roleId]
  );

  if (activeSessions[0].count > 0) {
    throw new Error('ROLE_IN_USE');
  }

  // 软删除角色
  await query(
    'UPDATE ai_roles SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [roleId]
  );

  return true;
}

/**
 * 获取订单列表（分页）
 */
async function getOrderList(page = 1, limit = 20, filters = {}) {
  const offset = (page - 1) * limit;

  let whereClause = 'WHERE 1=1';
  let queryParams = [];

  // 状态筛选
  if (filters.status) {
    whereClause += ' AND o.status = ?';
    queryParams.push(filters.status);
  }

  // 用户搜索（邮箱或UUID）
  if (filters.user_search) {
    whereClause += ' AND (u.email LIKE ? OR u.uuid LIKE ?)';
    queryParams.push(`%${filters.user_search}%`, `%${filters.user_search}%`);
  }

  // 订单搜索（订单UUID）
  if (filters.order_search) {
    whereClause += ' AND o.uuid LIKE ?';
    queryParams.push(`%${filters.order_search}%`);
  }

  // 获取总数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM orders o
    JOIN users u ON o.user_id = u.id
    JOIN plans p ON o.plan_id = p.id
    ${whereClause}
  `;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;

  // 获取订单列表
  const listQuery = `
    SELECT
      o.uuid, o.amount, o.status, o.payment_gateway, o.gateway_transaction_id, o.created_at, o.updated_at,
      u.uuid as user_uuid, u.email as user_email, u.is_guest,
      p.name as plan_name, p.plan_type
    FROM orders o
    JOIN users u ON o.user_id = u.id
    JOIN plans p ON o.plan_id = p.id
    ${whereClause}
    ORDER BY o.created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;
  const orders = await query(listQuery, queryParams);

  // 转换布尔值
  orders.forEach(order => {
    order.is_guest = Boolean(order.is_guest);
  });

  return {
    orders,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
}

/**
 * 创建套餐
 */
async function createPlan(planData) {
  const { name, description, price, plan_type, message_credits, duration_days } = planData;

  // 检查套餐名称是否已存在
  const existingPlans = await query(
    'SELECT COUNT(*) as count FROM plans WHERE name = ? AND is_active = 1',
    [name]
  );

  if (existingPlans[0].count > 0) {
    throw new Error('PLAN_NAME_EXISTS');
  }

  // 创建新套餐
  const result = await query(
    'INSERT INTO plans (name, description, price, plan_type, message_credits, duration_days, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [name, description || null, price, plan_type, message_credits || null, duration_days || null, true]
  );

  return {
    id: result.insertId,
    name,
    description: description || null,
    price: parseFloat(price),
    plan_type,
    message_credits: message_credits || null,
    duration_days: duration_days || null,
    is_active: true
  };
}

/**
 * 更新套餐
 */
async function updatePlan(planId, updateData) {
  // 检查套餐是否存在
  const plans = await query(
    'SELECT id, name, plan_type FROM plans WHERE id = ?',
    [planId]
  );

  if (plans.length === 0) {
    throw new Error('PLAN_NOT_FOUND');
  }

  const plan = plans[0];

  // 如果更新名称，检查是否与其他套餐重复
  if (updateData.name && updateData.name !== plan.name) {
    const existingPlans = await query(
      'SELECT COUNT(*) as count FROM plans WHERE name = ? AND id != ? AND is_active = 1',
      [updateData.name, planId]
    );

    if (existingPlans[0].count > 0) {
      throw new Error('PLAN_NAME_EXISTS');
    }
  }

  // 验证套餐类型相关字段的一致性
  if (updateData.message_credits !== undefined || updateData.duration_days !== undefined) {
    if (plan.plan_type === 'one_time' && updateData.duration_days !== undefined) {
      throw new Error('ONE_TIME_PLAN_CANNOT_HAVE_DURATION');
    }
    if (plan.plan_type === 'subscription' && updateData.message_credits !== undefined) {
      throw new Error('SUBSCRIPTION_PLAN_CANNOT_HAVE_CREDITS');
    }
  }

  // 构建更新字段
  const updateFields = [];
  const updateValues = [];

  Object.keys(updateData).forEach(key => {
    if (updateData[key] !== undefined) {
      updateFields.push(`${key} = ?`);
      updateValues.push(updateData[key]);
    }
  });

  if (updateFields.length === 0) {
    throw new Error('NO_UPDATE_DATA');
  }

  updateFields.push('updated_at = CURRENT_TIMESTAMP');
  updateValues.push(planId);

  // 执行更新
  await query(
    `UPDATE plans SET ${updateFields.join(', ')} WHERE id = ?`,
    updateValues
  );

  // 返回更新后的套餐信息
  const updatedPlans = await query(
    'SELECT id, name, description, price, plan_type, message_credits, duration_days, is_active, created_at, updated_at FROM plans WHERE id = ?',
    [planId]
  );

  const updatedPlan = updatedPlans[0];
  updatedPlan.price = parseFloat(updatedPlan.price);

  return updatedPlan;
}

/**
 * 禁用套餐（软删除）
 */
async function disablePlan(planId) {
  // 检查套餐是否存在
  const plans = await query(
    'SELECT id, is_active FROM plans WHERE id = ?',
    [planId]
  );

  if (plans.length === 0) {
    throw new Error('PLAN_NOT_FOUND');
  }

  if (!plans[0].is_active) {
    throw new Error('PLAN_ALREADY_DISABLED');
  }

  // 检查是否有未完成的订单使用此套餐
  const activeOrders = await query(
    'SELECT COUNT(*) as count FROM orders WHERE plan_id = ? AND status = "pending"',
    [planId]
  );

  if (activeOrders[0].count > 0) {
    throw new Error('PLAN_HAS_PENDING_ORDERS');
  }

  // 禁用套餐
  await query(
    'UPDATE plans SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [planId]
  );

  return true;
}

module.exports = {
  getUserList,
  getUserByUuid,
  updateUserStatus,
  getSystemStats,
  getUserChatSessions,
  getUserOrders,
  createAiRole,
  updateAiRole,
  deleteAiRole,
  getOrderList,
  createPlan,
  updatePlan,
  disablePlan
};
