const { query } = require('../config/database');

/**
 * 获取用户列表（分页）
 */
async function getUserList(page = 1, limit = 20, search = '') {
  const offset = (page - 1) * limit;
  
  let whereClause = '';
  let queryParams = [];
  
  if (search) {
    whereClause = 'WHERE email LIKE ? OR uuid LIKE ?';
    queryParams = [`%${search}%`, `%${search}%`];
  }
  
  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
  const countResult = await query(countQuery, queryParams);
  const total = countResult[0].total;
  
  // 获取用户列表
  const listQuery = `
    SELECT uuid, email, is_guest, role, status, daily_message_count, message_credits, created_at, updated_at
    FROM users
    ${whereClause}
    ORDER BY created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `;
  const users = await query(listQuery, queryParams);
  
  // 转换布尔值
  users.forEach(user => {
    user.is_guest = Boolean(user.is_guest);
  });
  
  return {
    users,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
}

/**
 * 根据UUID获取用户详情
 */
async function getUserByUuid(userUuid) {
  const users = await query(
    'SELECT uuid, email, is_guest, role, status, daily_message_count, message_credits, profile_summary, created_at, updated_at FROM users WHERE uuid = ?',
    [userUuid]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];
  user.is_guest = Boolean(user.is_guest);
  
  return user;
}

/**
 * 更新用户状态
 */
async function updateUserStatus(userUuid, status) {
  // 检查用户是否存在
  const users = await query(
    'SELECT id, role FROM users WHERE uuid = ?',
    [userUuid]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];

  // 防止管理员被禁用
  if (user.role === 'admin' && status === 'banned') {
    throw new Error('CANNOT_BAN_ADMIN');
  }

  // 更新用户状态
  await query(
    'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE uuid = ?',
    [status, userUuid]
  );

  return true;
}

/**
 * 获取系统统计信息
 */
async function getSystemStats() {
  // 用户统计
  const userStats = await query(`
    SELECT 
      COUNT(*) as total_users,
      SUM(CASE WHEN is_guest = 1 THEN 1 ELSE 0 END) as guest_users,
      SUM(CASE WHEN is_guest = 0 THEN 1 ELSE 0 END) as registered_users,
      SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
      SUM(CASE WHEN status = 'banned' THEN 1 ELSE 0 END) as banned_users
    FROM users
  `);

  // 今日新增用户
  const todayUsers = await query(`
    SELECT COUNT(*) as today_new_users 
    FROM users 
    WHERE DATE(created_at) = CURDATE()
  `);

  // 聊天会话统计
  const sessionStats = await query(`
    SELECT 
      COUNT(*) as total_sessions,
      COUNT(DISTINCT user_id) as active_chat_users
    FROM chat_sessions
  `);

  // 今日聊天会话
  const todaySessions = await query(`
    SELECT COUNT(*) as today_sessions 
    FROM chat_sessions 
    WHERE DATE(created_at) = CURDATE()
  `);

  // 消息统计
  const messageStats = await query(`
    SELECT 
      COUNT(*) as total_messages,
      SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as user_messages,
      SUM(CASE WHEN role = 'assistant' THEN 1 ELSE 0 END) as ai_messages
    FROM chat_messages
  `);

  // 今日消息
  const todayMessages = await query(`
    SELECT COUNT(*) as today_messages 
    FROM chat_messages 
    WHERE DATE(created_at) = CURDATE()
  `);

  // 订单统计
  const orderStats = await query(`
    SELECT 
      COUNT(*) as total_orders,
      SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
      SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue
    FROM orders
  `);

  // 今日订单
  const todayOrders = await query(`
    SELECT 
      COUNT(*) as today_orders,
      SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as today_revenue
    FROM orders 
    WHERE DATE(created_at) = CURDATE()
  `);

  return {
    users: {
      total: userStats[0].total_users,
      guest: userStats[0].guest_users,
      registered: userStats[0].registered_users,
      active: userStats[0].active_users,
      banned: userStats[0].banned_users,
      today_new: todayUsers[0].today_new_users
    },
    chats: {
      total_sessions: sessionStats[0].total_sessions,
      active_chat_users: sessionStats[0].active_chat_users,
      today_sessions: todaySessions[0].today_sessions,
      total_messages: messageStats[0].total_messages,
      user_messages: messageStats[0].user_messages,
      ai_messages: messageStats[0].ai_messages,
      today_messages: todayMessages[0].today_messages
    },
    orders: {
      total: orderStats[0].total_orders,
      completed: orderStats[0].completed_orders,
      total_revenue: parseFloat(orderStats[0].total_revenue || 0),
      today_orders: todayOrders[0].today_orders,
      today_revenue: parseFloat(todayOrders[0].today_revenue || 0)
    }
  };
}

/**
 * 获取用户的聊天会话列表
 */
async function getUserChatSessions(userUuid) {
  // 先获取用户ID
  const users = await query('SELECT id FROM users WHERE uuid = ?', [userUuid]);
  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const userId = users[0].id;

  const sessions = await query(
    'SELECT cs.uuid, cs.title, cs.created_at, cs.updated_at, ar.name as ai_role_name ' +
    'FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.user_id = ? ' +
    'ORDER BY cs.updated_at DESC',
    [userId]
  );

  return sessions;
}

/**
 * 获取用户的订单列表
 */
async function getUserOrders(userUuid) {
  // 先获取用户ID
  const users = await query('SELECT id FROM users WHERE uuid = ?', [userUuid]);
  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const userId = users[0].id;

  const orders = await query(
    'SELECT o.uuid, o.amount, o.status, o.created_at, p.name as plan_name ' +
    'FROM orders o ' +
    'JOIN plans p ON o.plan_id = p.id ' +
    'WHERE o.user_id = ? ' +
    'ORDER BY o.created_at DESC',
    [userId]
  );

  return orders;
}

module.exports = {
  getUserList,
  getUserByUuid,
  updateUserStatus,
  getSystemStats,
  getUserChatSessions,
  getUserOrders
};
