-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_chat_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ai_chat_app;

-- 创建用户表
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户内部ID',
  `uuid` CHAR(36) NOT NULL COMMENT '用户公开ID',
  `email` VARCHAR(255) NULL COMMENT '邮箱',
  `password_hash` VARCHAR(255) NULL COMMENT '加盐哈希后的密码',
  `is_guest` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否为游客账户',
  `status` ENUM('active', 'banned') NOT NULL DEFAULT 'active' COMMENT '账户状态 (active, banned)',
  `daily_message_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日消息使用计数',
  `last_usage_date` DATE NULL COMMENT '上次使用日期，用于重置每日计数',
  `message_credits` INT NOT NULL DEFAULT 0 COMMENT '剩余消息点数/条数',
  `profile_summary` TEXT NULL COMMENT '用户长期记忆摘要',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建AI角色表（为后续模块准备）
CREATE TABLE `ai_roles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'AI角色ID',
  `name` VARCHAR(100) NOT NULL COMMENT '角色名称',
  `avatar_url` VARCHAR(255) NULL COMMENT '角色头像URL',
  `description` VARCHAR(500) NULL COMMENT '角色一句话简介',
  `details` TEXT NULL COMMENT '角色详细介绍',
  `system_prompt` TEXT NOT NULL COMMENT '给AI的核心人设指令(System Prompt)',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用该角色',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色配置表';

-- 插入一些示例AI角色数据
INSERT INTO `ai_roles` (`name`, `avatar_url`, `description`, `details`, `system_prompt`) VALUES
('温柔的心理顾问', 'https://example.com/avatars/counselor.png', '倾听你的烦恼，给予你温暖的支持和专业的建议。', '她是一位温暖而专业的心理咨询师，拥有丰富的心理学知识和人生阅历。她善于倾听，能够理解你的情感需求，并给出贴心的建议。', '你是一位温柔、耐心的心理顾问。你善于倾听，能够理解用户的情感需求，并给出专业而贴心的建议。请用温暖、支持性的语言与用户交流，帮助他们缓解压力和焦虑。'),
('博学的历史学家', 'https://example.com/avatars/historian.png', '精通世界历史，能与你深入探讨任何历史事件。', '他是一位沉浸在时间长河中的学者，书房里堆满了古籍和地图。他不仅知道重大事件的始末，更了解那些不为人知的历史细节。', '你是一位博学的历史学家，对世界各国历史都有深入的了解。你能够生动地讲述历史事件，分析历史背景和影响，并帮助用户理解历史的复杂性和趣味性。请用学者的严谨和故事家的生动来回答问题。'),
('幽默的生活伙伴', 'https://example.com/avatars/companion.png', '用幽默和智慧陪伴你度过每一天。', '他是一个乐观开朗的朋友，总能在生活的琐碎中找到乐趣。他用幽默化解烦恼，用智慧点亮生活。', '你是一个幽默风趣、乐观开朗的生活伙伴。你善于用幽默来化解用户的烦恼，能够在日常生活中找到乐趣和智慧。请用轻松愉快的语调与用户交流，让他们感受到生活的美好。');
