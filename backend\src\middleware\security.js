const { sanitizeText, checkRateLimit } = require('../utils/security');

/**
 * 输入清理中间件
 * 清理请求体中的所有字符串字段
 */
function sanitizeInput(options = {}) {
  const {
    maxLength = 10000,
    allowNewlines = true,
    allowSpecialChars = true,
    skipFields = []
  } = options;

  return (req, res, next) => {
    try {
      // 清理请求体
      if (req.body && typeof req.body === 'object') {
        req.body = sanitizeObject(req.body, {
          maxLength,
          allowNewlines,
          allowSpecialChars,
          skipFields
        });
      }

      // 清理查询参数
      if (req.query && typeof req.query === 'object') {
        req.query = sanitizeObject(req.query, {
          maxLength: 1000, // 查询参数通常较短
          allowNewlines: false,
          allowSpecialChars: false,
          skipFields
        });
      }

      next();
    } catch (error) {
      console.error('输入清理失败:', error);
      res.error('请求参数格式错误', 40001, 400);
    }
  };
}

/**
 * 递归清理对象中的字符串字段
 */
function sanitizeObject(obj, options) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const { skipFields = [] } = options;
  const result = Array.isArray(obj) ? [] : {};

  for (const [key, value] of Object.entries(obj)) {
    // 跳过指定字段（如密码字段）
    if (skipFields.includes(key)) {
      result[key] = value;
      continue;
    }

    if (typeof value === 'string') {
      result[key] = sanitizeText(value, options);
    } else if (typeof value === 'object' && value !== null) {
      result[key] = sanitizeObject(value, options);
    } else {
      result[key] = value;
    }
  }

  return result;
}

/**
 * 频率限制中间件
 */
function rateLimit(options = {}) {
  const {
    maxRequests = 100,
    windowMs = 60000,
    keyGenerator = (req) => req.ip,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    onLimitReached = null
  } = options;

  return (req, res, next) => {
    try {
      const key = keyGenerator(req);
      const result = checkRateLimit(key, maxRequests, windowMs);

      // 设置响应头
      res.set({
        'X-RateLimit-Limit': maxRequests,
        'X-RateLimit-Remaining': result.remaining,
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      });

      if (!result.allowed) {
        if (onLimitReached) {
          onLimitReached(req, res);
        }
        
        return res.error(
          '请求过于频繁，请稍后再试',
          42901,
          429,
          {
            retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
          }
        );
      }

      // 如果需要跳过成功或失败的请求，在响应结束时处理
      if (skipSuccessfulRequests || skipFailedRequests) {
        const originalEnd = res.end;
        res.end = function(...args) {
          const shouldSkip = 
            (skipSuccessfulRequests && res.statusCode < 400) ||
            (skipFailedRequests && res.statusCode >= 400);

          if (shouldSkip) {
            // 从记录中移除这次请求
            // 这里简化处理，实际实现可能需要更复杂的逻辑
          }

          originalEnd.apply(this, args);
        };
      }

      next();
    } catch (error) {
      console.error('频率限制检查失败:', error);
      next(); // 出错时不阻止请求
    }
  };
}

/**
 * 聊天消息专用的频率限制
 */
function chatRateLimit() {
  return rateLimit({
    maxRequests: 30, // 每分钟最多30条消息
    windowMs: 60000, // 1分钟窗口
    keyGenerator: (req) => {
      // 使用用户ID作为限制键
      return req.user ? `chat_${req.user.id}` : `chat_ip_${req.ip}`;
    },
    onLimitReached: (req, res) => {
      console.warn(`用户 ${req.user?.id || req.ip} 触发聊天频率限制`);
    }
  });
}

/**
 * 登录频率限制
 */
function loginRateLimit() {
  return rateLimit({
    maxRequests: 5, // 每15分钟最多5次登录尝试
    windowMs: 15 * 60 * 1000, // 15分钟窗口
    keyGenerator: (req) => `login_${req.ip}`,
    skipSuccessfulRequests: true, // 成功登录不计入限制
    onLimitReached: (req, res) => {
      console.warn(`IP ${req.ip} 触发登录频率限制`);
    }
  });
}

/**
 * 注册频率限制
 */
function registerRateLimit() {
  return rateLimit({
    maxRequests: 3, // 每小时最多3次注册
    windowMs: 60 * 60 * 1000, // 1小时窗口
    keyGenerator: (req) => `register_${req.ip}`,
    onLimitReached: (req, res) => {
      console.warn(`IP ${req.ip} 触发注册频率限制`);
    }
  });
}

/**
 * 安全头中间件
 */
function securityHeaders() {
  return (req, res, next) => {
    // 设置安全相关的HTTP头
    res.set({
      // 防止点击劫持
      'X-Frame-Options': 'DENY',
      
      // 防止MIME类型嗅探
      'X-Content-Type-Options': 'nosniff',
      
      // XSS保护
      'X-XSS-Protection': '1; mode=block',
      
      // 引用策略
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      
      // 内容安全策略（根据需要调整）
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;",
      
      // 严格传输安全（仅在HTTPS环境下启用）
      ...(req.secure && {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
      })
    });

    next();
  };
}

/**
 * 请求大小限制中间件
 */
function requestSizeLimit(maxSize = '10mb') {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('content-length') || '0');
    const maxBytes = parseSize(maxSize);

    if (contentLength > maxBytes) {
      return res.error('请求体过大', 41301, 413);
    }

    next();
  };
}

/**
 * 解析大小字符串为字节数
 */
function parseSize(size) {
  if (typeof size === 'number') {
    return size;
  }

  const units = {
    b: 1,
    kb: 1024,
    mb: 1024 * 1024,
    gb: 1024 * 1024 * 1024
  };

  const match = size.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*(b|kb|mb|gb)?$/);
  if (!match) {
    throw new Error('Invalid size format');
  }

  const value = parseFloat(match[1]);
  const unit = match[2] || 'b';

  return Math.floor(value * units[unit]);
}

module.exports = {
  sanitizeInput,
  rateLimit,
  chatRateLimit,
  loginRateLimit,
  registerRateLimit,
  securityHeaders,
  requestSizeLimit
};
