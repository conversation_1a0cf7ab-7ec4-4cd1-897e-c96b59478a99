const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');

describe('聊天消息重复存储修复测试', () => {
  let userToken;
  let sessionUuid;
  const testEmail = `chat_dup_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 创建测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;

    // 获取AI角色
    const rolesResponse = await request(app)
      .get('/api/v1/ai-roles');

    const validRoleId = rolesResponse.body.data[0].id;

    // 创建聊天会话
    const sessionResponse = await request(app)
      .post('/api/v1/chat/sessions')
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        ai_role_id: validRoleId
      });

    sessionUuid = sessionResponse.body.data.uuid;
  });

  test('发送消息不应该产生重复存储', async () => {
    const testMessage = `测试消息_${Date.now()}`;
    
    // 获取发送前的消息数量
    const beforeMessages = await query(
      'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
      [sessionUuid]
    );
    const beforeCount = beforeMessages[0].count;

    // 发送消息
    const response = await request(app)
      .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        content: testMessage
      })
      .expect(200);

    expect(response.body.code).toBe(200);

    // 等待AI回复完成
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 获取发送后的消息数量
    const afterMessages = await query(
      'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
      [sessionUuid]
    );
    const afterCount = afterMessages[0].count;

    // 应该只增加2条消息：1条用户消息 + 1条AI回复
    expect(afterCount - beforeCount).toBe(2);

    // 检查是否有重复的用户消息
    const userMessages = await query(
      'SELECT content, COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?) AND role = "user" AND content = ? GROUP BY content',
      [sessionUuid, testMessage]
    );

    expect(userMessages.length).toBe(1);
    expect(userMessages[0].count).toBe(1);

    // 检查是否有重复的AI回复
    const aiMessages = await query(
      'SELECT content, COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?) AND role = "assistant" GROUP BY content HAVING COUNT(*) > 1',
      [sessionUuid]
    );

    // 不应该有重复的AI回复
    expect(aiMessages.length).toBe(0);
  });

  test('连续发送多条消息不应该产生重复', async () => {
    const messages = [
      `连续测试1_${Date.now()}`,
      `连续测试2_${Date.now()}`,
      `连续测试3_${Date.now()}`
    ];

    // 获取发送前的消息数量
    const beforeMessages = await query(
      'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
      [sessionUuid]
    );
    const beforeCount = beforeMessages[0].count;

    // 连续发送消息
    for (const message of messages) {
      await request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: message
        })
        .expect(200);

      // 等待一小段时间避免并发问题
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 等待所有AI回复完成
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 获取发送后的消息数量
    const afterMessages = await query(
      'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
      [sessionUuid]
    );
    const afterCount = afterMessages[0].count;

    // 应该增加6条消息：3条用户消息 + 3条AI回复
    expect(afterCount - beforeCount).toBe(6);

    // 检查每条用户消息都只有一条记录
    for (const message of messages) {
      const userMessages = await query(
        'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?) AND role = "user" AND content = ?',
        [sessionUuid, message]
      );
      expect(userMessages[0].count).toBe(1);
    }
  });

  afterAll(async () => {
    // 清理测试数据
    if (sessionUuid) {
      await query(
        'DELETE FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
        [sessionUuid]
      );
      await query(
        'DELETE FROM chat_sessions WHERE uuid = ?',
        [sessionUuid]
      );
    }
  });
});
