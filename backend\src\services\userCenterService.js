const { query } = require('../config/database');
const { hashPassword, verifyPassword } = require('../utils/password');
const { hasActiveSubscription, getUserSubscriptions } = require('./paymentService');

/**
 * 获取用户详细信息（包含会员状态）
 */
async function getUserProfile(userId) {
  const users = await query(
    'SELECT uuid, email, is_guest, status, daily_message_count, message_credits, profile_summary, created_at FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];
  // 转换布尔值
  user.is_guest = <PERSON><PERSON><PERSON>(user.is_guest);

  // 获取会员状态信息
  const membershipInfo = await getUserMembershipInfo(userId);

  return {
    ...user,
    membership: membershipInfo
  };
}

/**
 * 获取用户会员信息
 */
async function getUserMembershipInfo(userId) {
  try {
    // 检查是否有活跃订阅
    const hasActiveSub = await hasActiveSubscription(userId);

    if (!hasActiveSub) {
      return {
        is_member: false,
        subscription_type: null,
        expires_at: null,
        plan_name: null,
        remaining_days: 0
      };
    }

    // 获取当前活跃的订阅详情
    const subscriptions = await getUserSubscriptions(userId);
    const activeSubscription = subscriptions.find(sub =>
      sub.status === 'active' && new Date(sub.expires_at) > new Date()
    );

    if (!activeSubscription) {
      return {
        is_member: false,
        subscription_type: null,
        expires_at: null,
        plan_name: null,
        remaining_days: 0
      };
    }

    // 计算剩余天数
    const now = new Date();
    const expiresAt = new Date(activeSubscription.expires_at);
    const remainingDays = Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24));

    return {
      is_member: true,
      subscription_type: activeSubscription.plan_type,
      expires_at: activeSubscription.expires_at,
      plan_name: activeSubscription.plan_name,
      remaining_days: Math.max(0, remainingDays),
      subscription_id: activeSubscription.id
    };
  } catch (error) {
    console.error('获取会员信息失败:', error);
    return {
      is_member: false,
      subscription_type: null,
      expires_at: null,
      plan_name: null,
      remaining_days: 0
    };
  }
}

/**
 * 获取用户的所有聊天会话列表
 */
async function getUserChatSessions(userId) {
  const sessions = await query(
    'SELECT cs.uuid, cs.title, cs.ai_role_id, ar.name as ai_role_name, cs.updated_at ' +
    'FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.user_id = ? ' +
    'ORDER BY cs.updated_at DESC',
    [userId]
  );

  return sessions;
}

/**
 * 修改用户密码
 */
async function changeUserPassword(userId, oldPassword, newPassword) {
  // 获取用户当前密码哈希
  const users = await query(
    'SELECT password_hash, is_guest FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];

  // 检查是否为游客用户
  if (user.is_guest) {
    throw new Error('GUEST_CANNOT_CHANGE_PASSWORD');
  }

  // 验证旧密码
  const isOldPasswordValid = await verifyPassword(oldPassword, user.password_hash);
  if (!isOldPasswordValid) {
    throw new Error('INVALID_OLD_PASSWORD');
  }

  // 检查新密码是否与旧密码相同
  const isSamePassword = await verifyPassword(newPassword, user.password_hash);
  if (isSamePassword) {
    throw new Error('NEW_PASSWORD_SAME_AS_OLD');
  }

  // 哈希新密码
  const newPasswordHash = await hashPassword(newPassword);

  // 更新密码
  await query(
    'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [newPasswordHash, userId]
  );

  return true;
}

/**
 * 更新用户资料摘要
 */
async function updateUserProfileSummary(userId, profileSummary) {
  await query(
    'UPDATE users SET profile_summary = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [profileSummary, userId]
  );

  return true;
}

module.exports = {
  getUserProfile,
  getUserMembershipInfo,
  getUserChatSessions,
  changeUserPassword,
  updateUserProfileSummary
};
