const { query } = require('../config/database');
const { hashPassword, verifyPassword } = require('../utils/password');

/**
 * 获取用户详细信息
 */
async function getUserProfile(userId) {
  const users = await query(
    'SELECT uuid, email, is_guest, status, daily_message_count, message_credits, profile_summary, created_at FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];
  // 转换布尔值
  user.is_guest = <PERSON><PERSON>an(user.is_guest);
  
  return user;
}

/**
 * 获取用户的所有聊天会话列表
 */
async function getUserChatSessions(userId) {
  const sessions = await query(
    'SELECT cs.uuid, cs.title, cs.ai_role_id, ar.name as ai_role_name, cs.updated_at ' +
    'FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.user_id = ? ' +
    'ORDER BY cs.updated_at DESC',
    [userId]
  );

  return sessions;
}

/**
 * 修改用户密码
 */
async function changeUserPassword(userId, oldPassword, newPassword) {
  // 获取用户当前密码哈希
  const users = await query(
    'SELECT password_hash, is_guest FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];

  // 检查是否为游客用户
  if (user.is_guest) {
    throw new Error('GUEST_CANNOT_CHANGE_PASSWORD');
  }

  // 验证旧密码
  const isOldPasswordValid = await verifyPassword(oldPassword, user.password_hash);
  if (!isOldPasswordValid) {
    throw new Error('INVALID_OLD_PASSWORD');
  }

  // 检查新密码是否与旧密码相同
  const isSamePassword = await verifyPassword(newPassword, user.password_hash);
  if (isSamePassword) {
    throw new Error('NEW_PASSWORD_SAME_AS_OLD');
  }

  // 哈希新密码
  const newPasswordHash = await hashPassword(newPassword);

  // 更新密码
  await query(
    'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [newPasswordHash, userId]
  );

  return true;
}

/**
 * 更新用户资料摘要
 */
async function updateUserProfileSummary(userId, profileSummary) {
  await query(
    'UPDATE users SET profile_summary = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [profileSummary, userId]
  );

  return true;
}

/**
 * 删除指定聊天会话的所有历史记录
 */
async function deleteChatSession(userId, sessionUuid) {
  // 首先检查会话是否存在且属于当前用户
  const sessions = await query(
    'SELECT id FROM chat_sessions WHERE uuid = ? AND user_id = ?',
    [sessionUuid, userId]
  );

  if (sessions.length === 0) {
    throw new Error('SESSION_NOT_FOUND');
  }

  const sessionId = sessions[0].id;

  // 删除会话中的所有消息
  await query(
    'DELETE FROM chat_messages WHERE session_id = ?',
    [sessionId]
  );

  // 删除会话记录
  await query(
    'DELETE FROM chat_sessions WHERE id = ?',
    [sessionId]
  );

  return true;
}

/**
 * 删除用户的所有聊天历史记录
 */
async function deleteAllChatHistory(userId) {
  // 获取用户的所有会话ID
  const sessions = await query(
    'SELECT id FROM chat_sessions WHERE user_id = ?',
    [userId]
  );

  if (sessions.length === 0) {
    return true; // 没有聊天记录，直接返回成功
  }

  const sessionIds = sessions.map(session => session.id);

  // 删除所有会话的消息
  await query(
    `DELETE FROM chat_messages WHERE session_id IN (${sessionIds.map(() => '?').join(',')})`,
    sessionIds
  );

  // 删除所有会话记录
  await query(
    'DELETE FROM chat_sessions WHERE user_id = ?',
    [userId]
  );

  return true;
}

module.exports = {
  getUserProfile,
  getUserChatSessions,
  changeUserPassword,
  updateUserProfileSummary,
  deleteChatSession,
  deleteAllChatHistory
};
