const { query } = require('../config/database');
const { hashPassword, verifyPassword } = require('../utils/password');
const { getUserSubscriptions, hasActiveSubscription } = require('./paymentService');

/**
 * 获取用户详细信息（包含会员状态）
 */
async function getUserProfile(userId) {
  const users = await query(
    'SELECT uuid, email, is_guest, status, daily_message_count, message_credits, profile_summary, created_at FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];
  // 转换布尔值
  user.is_guest = <PERSON><PERSON><PERSON>(user.is_guest);

  // 获取用户会员状态
  const isVip = await hasActiveSubscription(userId);
  user.is_vip = isVip;

  // 如果是会员，获取订阅详情
  if (isVip) {
    const subscriptions = await getUserSubscriptions(userId);
    user.subscription_info = subscriptions.length > 0 ? {
      plan_name: subscriptions[0].plan_name,
      start_date: subscriptions[0].start_date,
      end_date: subscriptions[0].end_date,
      status: subscriptions[0].status
    } : null;
  } else {
    user.subscription_info = null;
  }

  return user;
}

/**
 * 获取用户的所有聊天会话列表
 */
async function getUserChatSessions(userId) {
  const sessions = await query(
    'SELECT cs.uuid, cs.title, cs.ai_role_id, ar.name as ai_role_name, cs.updated_at ' +
    'FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.user_id = ? ' +
    'ORDER BY cs.updated_at DESC',
    [userId]
  );

  return sessions;
}

/**
 * 修改用户密码
 */
async function changeUserPassword(userId, oldPassword, newPassword) {
  // 获取用户当前密码哈希
  const users = await query(
    'SELECT password_hash, is_guest FROM users WHERE id = ?',
    [userId]
  );

  if (users.length === 0) {
    throw new Error('USER_NOT_FOUND');
  }

  const user = users[0];

  // 检查是否为游客用户
  if (user.is_guest) {
    throw new Error('GUEST_CANNOT_CHANGE_PASSWORD');
  }

  // 验证旧密码
  const isOldPasswordValid = await verifyPassword(oldPassword, user.password_hash);
  if (!isOldPasswordValid) {
    throw new Error('INVALID_OLD_PASSWORD');
  }

  // 检查新密码是否与旧密码相同
  const isSamePassword = await verifyPassword(newPassword, user.password_hash);
  if (isSamePassword) {
    throw new Error('NEW_PASSWORD_SAME_AS_OLD');
  }

  // 哈希新密码
  const newPasswordHash = await hashPassword(newPassword);

  // 更新密码
  await query(
    'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [newPasswordHash, userId]
  );

  return true;
}

/**
 * 更新用户资料摘要
 */
async function updateUserProfileSummary(userId, profileSummary) {
  await query(
    'UPDATE users SET profile_summary = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [profileSummary, userId]
  );

  return true;
}

/**
 * 获取用户统计信息
 */
async function getUserStats(userId) {
  // 获取聊天会话统计
  const sessionStats = await query(
    'SELECT COUNT(*) as total_sessions FROM chat_sessions WHERE user_id = ?',
    [userId]
  );

  // 获取消息统计
  const messageStats = await query(
    'SELECT COUNT(*) as total_messages FROM chat_messages cm ' +
    'JOIN chat_sessions cs ON cm.session_id = cs.id ' +
    'WHERE cs.user_id = ? AND cm.role = "user"',
    [userId]
  );

  // 获取订单统计
  const orderStats = await query(
    'SELECT COUNT(*) as total_orders, SUM(amount) as total_spent FROM orders WHERE user_id = ? AND status = "completed"',
    [userId]
  );

  // 获取会员状态
  const isVip = await hasActiveSubscription(userId);

  // 获取用户基本信息
  const userInfo = await query(
    'SELECT daily_message_count, message_credits, created_at FROM users WHERE id = ?',
    [userId]
  );

  const user = userInfo[0];

  return {
    total_sessions: sessionStats[0].total_sessions || 0,
    total_messages: messageStats[0].total_messages || 0,
    total_orders: orderStats[0].total_orders || 0,
    total_spent: parseFloat(orderStats[0].total_spent || 0),
    daily_message_count: user.daily_message_count || 0,
    message_credits: user.message_credits || 0,
    is_vip: isVip,
    member_since: user.created_at
  };
}

module.exports = {
  getUserProfile,
  getUserChatSessions,
  changeUserPassword,
  updateUserProfileSummary,
  getUserStats
};
