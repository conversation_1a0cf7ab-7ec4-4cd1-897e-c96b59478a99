-----

### **项目核心理念**

为用户提供一个安全、匿名、私密的空间，通过与不同人设的 AI 伙伴进行对话，释放情绪、梳理心事，获得情感支持与慰藉。项目覆盖 PC Web 和移动端 H5，保证用户在不同设备上拥有一致的体验。

### **开发里程碑模块划分**

为了实现最高效的开发流程，我将项目分解为以下六个核心开发模块。请务必按照此顺序进行开发，因为后一模块的功能会依赖前一模块的基础。

-----

### **模块一：用户认证与账户基础**

**目标：** 搭建项目的用户系统，支持匿名访问和正式注册，为后续所有功能提供用户身份基础。这是整个系统的基石。

| 功能点 | 功能描述 | 涉及角色 | 关键业务/技术逻辑 |
| :--- | :--- | :--- | :--- |
| **1.1 匿名/游客访问** | 用户首次进入网站时，系统自动为其创建一个临时的游客身份，无需注册。游客可以立即开始与 AI 对话。 | 游客 | - **后端 (Node.js):** 生成一个唯一的游客 `session_id` 或 `device_id`，并使用 JWT 生成一个临时 token 返回给客户端。\<br\>- **前端 (Vue3):** 将 token 存储在 `localStorage` 或 `sessionStorage` 中，后续请求携带此 token。\<br\>- **数据库 (MySQL):** 可在 `users` 表中创建一条 `is_guest=1` 的记录，用于关联聊天记录。 |
| **1.2 账号注册** | 用户可以通过“邮箱 + 密码”的方式注册正式账号。注册后，游客身份下的聊天记录可以（可选）迁移到正式账号下。 | 游客 | - **前端:** 提供注册表单，进行基本的前端校验（邮箱格式、密码强度）。\<br\>- **后端:** 接收注册信息，校验邮箱是否已存在。密码必须使用 `bcrypt` 等库加盐哈希后存储。\<br\>- **数据库:** 在 `users` 表中创建一条新记录，`is_guest=0`。 |
| **1.3 账号登录** | 已注册用户可以通过“邮箱 + 密码”进行登录。 | 注册用户 | - **后端:** 校验用户凭证，成功后生成正式的、有效期更长的 JWT token。\<br\>- **前端:** 替换并持久化存储新的 token，更新应用状态为“已登录”。 |
| **1.4 退出登录** | 用户可以主动退出当前账号。 | 注册用户 | - **前端:** 清除本地存储的 token，并重置应用状态到游客模式。 |

-----

### **模块二：AI 角色展示与选择**

**目标：** 打造产品的核心吸引力。设计一个可供用户选择的 AI 角色墙，让用户可以选择不同性格、背景、风格的 AI 进行对话，提升趣味性和代入感。

| 功能点 | 功能描述 | 涉及角色 | 关键业务/技术逻辑 |
| :--- | :--- | :--- | :--- |
| **2.1 AI 角色列表** | 以卡片形式展示所有可用的 AI 角色。每个卡片包含角色头像、名称、一句话简介（如：“温柔的倾听者”、“理性的分析师”）。 | 所有用户 | - **前端:** 设计响应式卡片布局，适配 PC 和移动端。\<br\>- **后端:** 提供一个无需鉴权的 API 接口，用于获取所有 AI 角色信息。\<br\>- **数据库:** 创建 `ai_roles` 表，包含 `id`, `name`, `avatar_url`, `description`, `system_prompt` (核心人设指令)等字段。 |
| **2.2 AI 角色详情** | 点击角色卡片，可以展开或跳转到详情页，查看该角色的详细介绍、性格标签、擅长领域等。 | 所有用户 | - 可以通过弹窗或新页面实现。展示 `ai_roles` 表中的详细信息。 |
| **2.3 选择并开始对话** | 用户在列表或详情页点击“开始聊天”按钮，进入与该特定 AI 角色的聊天界面。 | 所有用户 | - **前端:** 记录用户选择的 `role_id`，并跳转到聊天页面，将 `role_id` 作为参数传递。 |

-----

### **模块三：核心对话功能**

**目标：** 实现产品最核心的聊天功能，保证对话流程的顺畅、稳定和优秀的用户体验。

| 功能点 | 功能描述 | 涉及角色 | 关键业务/技术逻辑 |
| :--- | :--- | :--- | :--- |
| **3.1 聊天界面** | 经典的即时通讯界面布局，上方显示 AI 角色信息，中间是消息记录区，下方是文本输入框和发送按钮。 | 所有用户 | - **前端 (Vue3):** 使用 `ref` 或 `reactive` 管理聊天记录数组。消息列表自动滚动到底部。 |
| **3.2 消息发送与接收** | 用户输入文字后发送。界面上实时显示 AI “正在输入中”的状态，然后展示 AI 的回复。 | 所有用户 | - **后端 (Node.js):** 创建一个 `/chat` 接口，接收用户消息 `content` 和 `role_id`。\<br\>- 调用 AI API (如 OpenAI, Gemini等)，将 `ai_roles` 表中的 `system_prompt`、历史对话记录和用户新消息一同发送。\<br\>- **强烈建议使用流式传输 (Streaming):** 后端接收到 AI API 的流式响应后，立即转发给前端。前端逐字显示，极大提升用户体验。 |
| **3.3 对话上下文管理** | AI 需要能够理解对话的上下文。每次请求时，需要将最近的几轮对话历史一并发送给 AI 模型。 | 所有用户 | - **后端:** 从数据库中获取当前会话的历史消息（例如最近10条），与新消息一同构建成发送给 AI API 的 `messages` 数组。\<br\>- **数据库:** 创建 `chat_messages` 表，包含 `id`, `session_id`, `user_id`, `role` (user/assistant), `content`, `timestamp`。 |
| **3.4 聊天会话管理** | 用户与不同 AI 角色的对话应被视为独立的会话。 | 所有用户 | - **前端:** 在进入聊天时，可以生成或获取一个 `session_id`，用于标识本次会话。\<br\>- **后端:** 所有 `chat_messages` 记录都关联一个 `session_id`。 |

-----

### **模块四：用户中心**

**目标：** 为注册用户提供账户管理和历史记录追溯功能，增强用户粘性。

| 功能点 | 功能描述 | 涉及角色 | 关键业务/技术逻辑 |
| :--- | :--- | :--- | :--- |
| **4.1 对话历史列表** | 展示该用户所有的历史对话会话列表，按时间倒序排列。每项显示对话的 AI 角色、最后一条消息摘要和时间。 | 注册用户 | - **后端:** 提供一个接口，根据 `user_id` 查询 `chat_messages` 表，通过 `session_id` 进行分组，并获取每个会话的最新消息。\<br\>- **数据库:** 查询需要进行 `GROUP BY session_id` 和排序。 |
| **4.2 历史对话查看** | 点击任一历史会话，可以进入该会话的完整聊天记录页面，进行回顾。 | 注册用户 | - **前端:** 复用核心对话界面，但设为只读模式。\<br\>- **后端:** 提供接口，根据 `session_id` 获取该会话的全部消息记录。 |
| **4.3 账户设置** | 提供修改密码等基础账户管理功能。 | 注册用户 | - 标准的账户管理流程，后端接口需要校验用户旧密码。 |

-----

### **模块五：支付与充值（可选，商业化模块）**

**目标：** 设计商业化路径。由于 AI API 调用有成本，通过提供增值服务实现盈利。

| 功能点 | 功能描述 | 涉及角色 | 关键业务/技术逻辑 |
| :--- | :--- | :--- | :--- |
| **5.1 免费额度与限制** | 游客和免费注册用户拥有有限的每日免费聊天次数或消息条数。超出后会收到提示。 | 所有用户 | - **后端:** 在 `/chat` 接口中增加计数逻辑。每次调用前，检查用户的当日使用量是否超出限制。\<br\>- **数据库:** 在 `users` 表中增加 `daily_usage_count` 和 `last_usage_date` 字段，或使用 Redis 进行更高性能的计数。 |
| **5.2 套餐与定价页** | 展示不同的充值套餐，如“消息包”（购买固定数量的消息条数）或“月度会员”（无限畅聊）。 | 注册用户 | - **前端:** 静态或动态的定价页面。 |
| **5.3 支付集成** | 集成支付宝或微信支付。用户选择套餐后，扫码或跳转至支付页面完成支付。 | 注册用户 | - **后端:** 集成支付 SDK，生成订单，获取支付二维码/链接。创建 `orders` 表记录订单状态。\<br\>- **关键:** 必须实现支付成功后的异步回调接口（Webhook），在收到支付平台通知后，为用户账户增加对应的权益（如消息条数）。 |

-----

### **模块六：后台管理系统**

**目标：** 为运营人员提供一个管理整个平台的工具，确保系统可控、可运营。

| 功能点 | 功能描述 | 涉及角色 | 管理员 |
| :--- | :--- | :--- | :--- |
| **6.1 数据看板** | 展示核心运营数据：新增用户数、日活跃用户数、AI 调用次数、订单总额等。 | 管理员 | - **后端:** 提供聚合查询接口，从各数据表中统计数据。\<br\>- **前端:** 使用图表库（如 ECharts）进行数据可视化。 |
| **6.2 用户管理** | 查看所有用户列表，可以搜索、查看用户详情、封禁/解封用户。 | 管理员 | - 对 `users` 表进行标准后台式的 CRUD 操作。 |
| **6.3 AI 角色管理** | 增、删、改、查所有 AI 角色。可以随时调整角色的名称、头像、以及最重要的 **System Prompt**，从而在线调整 AI 的人设和行为，无需重新部署代码。 | 管理员 | - 对 `ai_roles` 表进行 CRUD 操作。这是运营的核心功能之一。 |
| **6.4 订单管理** | 查看所有支付订单的记录和状态。 | 管理员 | - 对 `orders` 表的查询和管理。 |

-----

### **💡 亮点功能与扩展建议**

1.  **动态情绪分析与反馈 (Emotional Analysis & Feedback)**

      * **描述:** 在核心对话功能之上，后端每次接收到用户消息后，可以额外调用一个情感分析 API (或使用 NLP 库) 来识别用户当前的情绪（如：悲伤、焦虑、快乐）。然后，可以将这种情绪状态作为额外信息提供给主聊天 AI，让 AI 的回复更具同理心和针对性（例如，当识别到用户“悲伤”时，AI 的回复会更偏向安慰）。同时，可以在用户中心为用户生成一个“情绪日记”或“情绪曲线图”，帮助用户进行自我觉察，极大提升产品深度和价值。

2.  **长期记忆与个人摘要 (Long-term Memory & Personalization)**

      * **描述:** 当前的上下文管理只解决了短期记忆。可以引入一个异步任务：每当一次对话结束后，后端启动一个任务，让另一个 AI 模型（或特定 prompt）去“总结”这次对话的核心内容和用户的关键信息（如：“用户最近在为工作烦恼”、“用户的宠物叫‘咪咪’”）。将这些摘要信息存储在 `users` 表的一个 `profile_summary` 字段中。在下一次用户开始新的对话时，将这个摘要信息放入 `system_prompt`，让 AI “记起”用户，实现真正的个性化长期陪伴。这将是秒杀市面上多数竞品的杀手级功能。