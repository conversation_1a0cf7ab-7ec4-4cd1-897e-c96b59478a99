const request = require('supertest');
const app = require('../src/app');

describe('调试会员状态功能', () => {
  let userToken;
  const testEmail = `debug_membership_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 注册测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (registerResponse.body.code === 201) {
      userToken = registerResponse.body.data.token;
    }
  });

  test('测试用户个人信息接口', async () => {
    if (!userToken) {
      console.log('跳过测试：用户注册失败');
      return;
    }

    console.log('测试用户token:', userToken);

    const response = await request(app)
      .get('/api/v1/user/profile')
      .set('Authorization', `Bearer ${userToken}`);

    console.log('响应状态:', response.status);
    console.log('响应体:', JSON.stringify(response.body, null, 2));

    if (response.status !== 200) {
      console.log('错误详情:', response.text);
    }
  });

  test('测试用户统计接口', async () => {
    if (!userToken) {
      console.log('跳过测试：用户注册失败');
      return;
    }

    const response = await request(app)
      .get('/api/v1/user/stats')
      .set('Authorization', `Bearer ${userToken}`);

    console.log('统计接口响应状态:', response.status);
    console.log('统计接口响应体:', JSON.stringify(response.body, null, 2));

    if (response.status !== 200) {
      console.log('统计接口错误详情:', response.text);
    }
  });
});
