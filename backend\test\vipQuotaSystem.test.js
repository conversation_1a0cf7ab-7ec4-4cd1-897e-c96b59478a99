const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');
const { getDailyQuotaInfo, consumeQuota, checkQuotaAvailable } = require('../src/utils/dailyQuota');

describe('VIP用户额度系统测试', () => {
  let guestToken, userToken, vipUserToken;
  let guestUserId, normalUserId, vipUserId;
  let planId;

  beforeAll(async () => {
    // 1. 创建游客用户
    const guestResponse = await request(app)
      .post('/api/v1/auth/guest');
    
    if (guestResponse.body.code === 201) {
      guestToken = guestResponse.body.data.token;
      const guestUsers = await query('SELECT id FROM users WHERE uuid = ?', [guestResponse.body.data.user.uuid]);
      guestUserId = guestUsers[0].id;
    }

    // 2. 创建普通注册用户
    const testEmail = `quota_test_${Date.now()}@example.com`;
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: 'test123456'
      });

    if (userResponse.body.code === 201) {
      userToken = userResponse.body.data.token;
      const normalUsers = await query('SELECT id FROM users WHERE email = ?', [testEmail]);
      normalUserId = normalUsers[0].id;
    }

    // 3. 创建VIP用户（通过购买订阅）
    const vipEmail = `vip_test_${Date.now()}@example.com`;
    const vipResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: vipEmail,
        password: 'test123456'
      });

    if (vipResponse.body.code === 201) {
      vipUserToken = vipResponse.body.data.token;
      const vipUsers = await query('SELECT id FROM users WHERE email = ?', [vipEmail]);
      vipUserId = vipUsers[0].id;

      // 获取订阅套餐
      const plansResponse = await request(app).get('/api/v1/plans');
      const subscriptionPlan = plansResponse.body.data.find(plan => plan.plan_type === 'subscription');
      
      if (subscriptionPlan) {
        planId = subscriptionPlan.id;
        
        // 创建订单并支付
        const orderResponse = await request(app)
          .post('/api/v1/orders')
          .set('Authorization', `Bearer ${vipUserToken}`)
          .send({ plan_id: planId });

        if (orderResponse.body.code === 201) {
          // 模拟支付成功
          await request(app)
            .post('/api/v1/payments/notify')
            .send({
              order_uuid: orderResponse.body.data.order_uuid,
              status: 'success',
              transaction_id: `txn_${Date.now()}`
            });
        }
      }
    }
  });

  describe('不同用户类型的每日额度', () => {
    test('游客用户应该有10条每日额度', async () => {
      if (!guestUserId) {
        console.log('跳过测试：游客用户创建失败');
        return;
      }

      const quotaInfo = await getDailyQuotaInfo(guestUserId);
      
      expect(quotaInfo.daily_limit).toBe(10);
      expect(quotaInfo.is_guest).toBe(true);
      expect(quotaInfo.is_vip).toBe(false);
      expect(quotaInfo.remaining_daily).toBeLessThanOrEqual(10);
    });

    test('普通注册用户应该有50条每日额度', async () => {
      if (!normalUserId) {
        console.log('跳过测试：普通用户创建失败');
        return;
      }

      const quotaInfo = await getDailyQuotaInfo(normalUserId);
      
      expect(quotaInfo.daily_limit).toBe(50);
      expect(quotaInfo.is_guest).toBe(false);
      expect(quotaInfo.is_vip).toBe(false);
      expect(quotaInfo.remaining_daily).toBeLessThanOrEqual(50);
    });

    test('VIP用户应该有无限每日额度', async () => {
      if (!vipUserId) {
        console.log('跳过测试：VIP用户创建失败');
        return;
      }

      const quotaInfo = await getDailyQuotaInfo(vipUserId);
      
      console.log('VIP用户额度信息:', quotaInfo);
      
      expect(quotaInfo.daily_limit).toBeGreaterThan(50); // 应该远大于普通用户
      expect(quotaInfo.is_guest).toBe(false);
      expect(quotaInfo.is_vip).toBe(true);
      expect(quotaInfo.remaining_daily).toBeGreaterThan(50);
    });
  });

  describe('VIP用户额度消费测试', () => {
    test('VIP用户应该能够消费大量额度而不受限制', async () => {
      if (!vipUserId) {
        console.log('跳过测试：VIP用户创建失败');
        return;
      }

      // 连续消费100条消息（远超普通用户限制）
      for (let i = 0; i < 100; i++) {
        const consumeResult = await consumeQuota(vipUserId, 1);
        expect(consumeResult.success).toBe(true);
        expect(consumeResult.type).toBe('daily'); // 使用每日额度
      }

      // 检查还有额度可用
      const quotaCheck = await checkQuotaAvailable(vipUserId);
      expect(quotaCheck.available).toBe(true);
      expect(quotaCheck.remaining).toBeGreaterThan(0);
    });

    test('普通用户消费超过50条应该失败', async () => {
      if (!normalUserId) {
        console.log('跳过测试：普通用户创建失败');
        return;
      }

      // 先检查用户当前额度状态
      const initialQuota = await getDailyQuotaInfo(normalUserId);
      console.log('普通用户初始额度:', initialQuota);

      // 如果用户有消息点数，先清空
      if (initialQuota.message_credits > 0) {
        await query('UPDATE users SET message_credits = 0 WHERE id = ?', [normalUserId]);
      }

      // 重置每日消息计数
      await query('UPDATE users SET daily_message_count = 0, last_usage_date = CURRENT_DATE WHERE id = ?', [normalUserId]);

      // 先消费50条（达到限制）
      let successCount = 0;
      for (let i = 0; i < 50; i++) {
        const consumeResult = await consumeQuota(normalUserId, 1);
        if (consumeResult.success) {
          successCount++;
        } else {
          console.log(`第${i+1}条消费失败:`, consumeResult);
          break;
        }
      }

      console.log(`成功消费了 ${successCount} 条消息`);

      // 尝试再消费一条，应该失败
      const overLimitResult = await consumeQuota(normalUserId, 1);
      console.log('超限消费结果:', overLimitResult);

      expect(overLimitResult.success).toBe(false);
      expect(overLimitResult.type).toBe('insufficient');
    });
  });

  describe('API接口中的额度信息', () => {
    test('VIP用户的个人信息应该显示正确的额度', async () => {
      if (!vipUserToken) {
        console.log('跳过测试：VIP用户token未获取');
        return;
      }

      const response = await request(app)
        .get('/api/v1/user/profile')
        .set('Authorization', `Bearer ${vipUserToken}`)
        .expect(200);

      expect(response.body.data.is_vip).toBe(true);
      expect(response.body.data.subscription_info).not.toBeNull();
    });

    test('VIP用户的统计信息应该显示VIP状态', async () => {
      if (!vipUserToken) {
        console.log('跳过测试：VIP用户token未获取');
        return;
      }

      const response = await request(app)
        .get('/api/v1/user/stats')
        .set('Authorization', `Bearer ${vipUserToken}`)
        .expect(200);

      expect(response.body.data.is_vip).toBe(true);
    });
  });

  describe('聊天接口中的额度检查', () => {
    test('VIP用户发送消息应该不受每日限制', async () => {
      if (!vipUserToken) {
        console.log('跳过测试：VIP用户token未获取');
        return;
      }

      // 获取AI角色
      const rolesResponse = await request(app).get('/api/v1/ai-roles');
      const aiRole = rolesResponse.body.data[0];

      if (!aiRole) {
        console.log('跳过测试：没有可用的AI角色');
        return;
      }

      // 创建聊天会话
      const sessionResponse = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${vipUserToken}`)
        .send({
          ai_role_id: aiRole.id,
          title: 'VIP额度测试会话'
        });

      if (sessionResponse.body.code !== 201) {
        console.log('跳过测试：会话创建失败');
        return;
      }

      const sessionUuid = sessionResponse.body.data.uuid;

      // 发送多条消息测试（超过普通用户限制）
      for (let i = 0; i < 60; i++) {
        const messageResponse = await request(app)
          .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
          .set('Authorization', `Bearer ${vipUserToken}`)
          .send({
            content: `VIP测试消息 ${i + 1}`,
            role: 'user'
          });

        // VIP用户应该能够发送所有消息
        expect(messageResponse.status).toBe(201);
      }
    });
  });

  afterAll(async () => {
    // 清理测试数据
    if (guestUserId) {
      await query('DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id = ?)', [guestUserId]);
      await query('DELETE FROM chat_sessions WHERE user_id = ?', [guestUserId]);
      await query('DELETE FROM users WHERE id = ?', [guestUserId]);
    }
    
    if (normalUserId) {
      await query('DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id = ?)', [normalUserId]);
      await query('DELETE FROM chat_sessions WHERE user_id = ?', [normalUserId]);
      await query('DELETE FROM users WHERE id = ?', [normalUserId]);
    }
    
    if (vipUserId) {
      await query('DELETE FROM user_subscriptions WHERE user_id = ?', [vipUserId]);
      await query('DELETE FROM orders WHERE user_id = ?', [vipUserId]);
      await query('DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id = ?)', [vipUserId]);
      await query('DELETE FROM chat_sessions WHERE user_id = ?', [vipUserId]);
      await query('DELETE FROM users WHERE id = ?', [vipUserId]);
    }
  });
});
