# AI伙伴聊天应用 - 模块六API文档

## 概述

本文档描述了AI伙伴聊天应用**模块六：后台管理系统**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1/admin`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token（管理员权限）

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40301,
  "message": "需要管理员权限",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 400 | 40001 | 请求参数无效 |
| 401 | 40101 | 未授权或Token无效 |
| 403 | 40301 | 需要管理员权限 |
| 404 | 40401 | 请求的资源未找到 |
| 500 | 50001 | 服务器内部错误 |

## 权限说明

所有管理员接口都需要：
1. **有效的JWT Token**: 用户必须已登录
2. **管理员角色**: 用户的role字段必须为"admin"

**默认管理员账户**:
- 邮箱: `<EMAIL>`
- 密码: `admin123456`
- **请及时修改默认密码！**

---

## 接口列表

### 1. 获取系统统计信息

**接口描述**: 获取系统整体运营数据统计

- **URL**: `GET /stats`
- **认证**: 需要管理员权限
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "users": {
      "total": 1250,
      "guest": 800,
      "registered": 450,
      "active": 1200,
      "banned": 50,
      "today_new": 25
    },
    "chats": {
      "total_sessions": 3500,
      "active_chat_users": 680,
      "today_sessions": 120,
      "total_messages": 15000,
      "user_messages": 7500,
      "ai_messages": 7500,
      "today_messages": 500
    },
    "orders": {
      "total": 320,
      "completed": 280,
      "total_revenue": 5600.00,
      "today_orders": 8,
      "today_revenue": 160.00
    }
  }
}
```

**字段说明**:
- `users`: 用户相关统计
  - `total`: 总用户数
  - `guest`: 游客用户数
  - `registered`: 注册用户数
  - `active`: 活跃用户数
  - `banned`: 被禁用用户数
  - `today_new`: 今日新增用户数
- `chats`: 聊天相关统计
  - `total_sessions`: 总会话数
  - `active_chat_users`: 有聊天记录的用户数
  - `today_sessions`: 今日新增会话数
  - `total_messages`: 总消息数
  - `user_messages`: 用户消息数
  - `ai_messages`: AI回复数
  - `today_messages`: 今日消息数
- `orders`: 订单相关统计
  - `total`: 总订单数
  - `completed`: 已完成订单数
  - `total_revenue`: 总收入
  - `today_orders`: 今日订单数
  - `today_revenue`: 今日收入

### 2. 获取用户列表

**接口描述**: 获取系统所有用户列表，支持分页和搜索

- **URL**: `GET /users`
- **认证**: 需要管理员权限

**查询参数**:
| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码，从1开始 |
| limit | number | 否 | 20 | 每页数量，最大100 |
| search | string | 否 | - | 搜索关键词（邮箱或UUID） |

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "users": [
      {
        "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
        "email": "<EMAIL>",
        "is_guest": false,
        "role": "user",
        "status": "active",
        "daily_message_count": 5,
        "message_credits": 100,
        "created_at": "2025-07-20T10:00:00.000Z",
        "updated_at": "2025-07-22T15:30:00.000Z"
      },
      {
        "uuid": "c3d4e5f6-a7b8-9012-3456-7890abcdef12",
        "email": null,
        "is_guest": true,
        "role": "user",
        "status": "active",
        "daily_message_count": 2,
        "message_credits": 0,
        "created_at": "2025-07-22T09:15:00.000Z",
        "updated_at": "2025-07-22T09:15:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1250,
      "pages": 63
    }
  }
}
```

**特点**:
- 按创建时间倒序排列（最新的在前）
- 支持邮箱和UUID模糊搜索
- 完整的分页信息
- 包含用户的基本信息和使用统计

### 3. 获取用户详情

**接口描述**: 获取指定用户的详细信息

- **URL**: `GET /users/{user_uuid}`
- **认证**: 需要管理员权限

**路径参数**:
- `user_uuid`: 用户的UUID（必须是有效的UUID格式）

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
    "email": "<EMAIL>",
    "is_guest": false,
    "role": "user",
    "status": "active",
    "daily_message_count": 5,
    "message_credits": 100,
    "profile_summary": "该用户对古罗马历史表现出浓厚兴趣。",
    "created_at": "2025-07-20T10:00:00.000Z",
    "updated_at": "2025-07-22T15:30:00.000Z"
  }
}
```

**错误响应**:
- `404 Not Found`: 用户不存在
- `400 Bad Request`: UUID格式无效

### 4. 更新用户状态

**接口描述**: 更新指定用户的账户状态（激活/禁用）

- **URL**: `PUT /users/{user_uuid}/status`
- **认证**: 需要管理员权限

**路径参数**:
- `user_uuid`: 用户的UUID

**请求参数**:
```json
{
  "status": "banned"
}
```

**参数说明**:
- `status`: 用户状态，只能是"active"或"banned"

**响应示例**:
```json
{
  "code": 200,
  "message": "User status updated successfully",
  "data": null
}
```

**业务规则**:
- 不能禁用管理员账户
- 被禁用的用户无法登录系统
- 状态更新会立即生效

**错误响应**:
- `404 Not Found`: 用户不存在
- `400 Bad Request`: 不能禁用管理员账户或状态值无效

### 5. 获取用户的聊天会话列表

**接口描述**: 获取指定用户的所有聊天会话记录

- **URL**: `GET /users/{user_uuid}/chats`
- **认证**: 需要管理员权限

**路径参数**:
- `user_uuid`: 用户的UUID

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "uuid": "c3d4e5f6-a7b8-9012-3456-7890abcdef12",
      "title": "关于古罗马历史的探讨",
      "ai_role_name": "博学的历史学家",
      "created_at": "2025-07-21T10:00:00.000Z",
      "updated_at": "2025-07-21T23:24:00.000Z"
    },
    {
      "uuid": "d4e5f6a7-b8c9-0123-4567-890abcdef123",
      "title": "如何应对工作压力",
      "ai_role_name": "温柔的心理顾问",
      "created_at": "2025-07-20T15:30:00.000Z",
      "updated_at": "2025-07-20T18:30:00.000Z"
    }
  ]
}
```

**特点**:
- 按最后更新时间倒序排列
- 包含AI角色信息
- 显示会话标题和时间信息

### 6. 获取用户的订单列表

**接口描述**: 获取指定用户的所有订单记录

- **URL**: `GET /users/{user_uuid}/orders`
- **认证**: 需要管理员权限

**路径参数**:
- `user_uuid`: 用户的UUID

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "uuid": "e5f6a7b8-c9d0-1234-5678-90abcdef1234",
      "amount": "20.00",
      "status": "completed",
      "plan_name": "月度会员",
      "created_at": "2025-07-22T10:00:00.000Z"
    },
    {
      "uuid": "f6a7b8c9-d0e1-2345-6789-0abcdef12345",
      "amount": "50.00",
      "status": "completed",
      "plan_name": "消息加油包",
      "created_at": "2025-07-20T15:30:00.000Z"
    }
  ]
}
```

**特点**:
- 按创建时间倒序排列
- 包含套餐名称信息
- 显示订单状态和金额

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 管理员登录
async function adminLogin(email, password) {
  try {
    const response = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    const result = await response.json();
    if (result.code === 200) {
      localStorage.setItem('adminToken', result.data.token);
      return result.data.token;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('管理员登录失败:', error);
    throw error;
  }
}

// 获取系统统计
async function getSystemStats(token) {
  try {
    const response = await fetch('/api/v1/admin/stats', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取统计失败:', error);
    throw error;
  }
}

// 获取用户列表
async function getUserList(token, page = 1, limit = 20, search = '') {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    });
    
    if (search) {
      params.append('search', search);
    }

    const response = await fetch(`/api/v1/admin/users?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    throw error;
  }
}

// 更新用户状态
async function updateUserStatus(token, userUuid, status) {
  try {
    const response = await fetch(`/api/v1/admin/users/${userUuid}/status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ status })
    });

    const result = await response.json();
    if (result.code === 200) {
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新用户状态失败:', error);
    throw error;
  }
}

// 获取用户详情
async function getUserDetail(token, userUuid) {
  try {
    const response = await fetch(`/api/v1/admin/users/${userUuid}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    throw error;
  }
}
```

### Vue.js 管理后台组件示例

```vue
<template>
  <div class="admin-dashboard">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2>系统概览</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <h3>用户统计</h3>
          <p>总用户: {{ stats.users?.total || 0 }}</p>
          <p>注册用户: {{ stats.users?.registered || 0 }}</p>
          <p>今日新增: {{ stats.users?.today_new || 0 }}</p>
        </div>
        <div class="stat-card">
          <h3>聊天统计</h3>
          <p>总会话: {{ stats.chats?.total_sessions || 0 }}</p>
          <p>总消息: {{ stats.chats?.total_messages || 0 }}</p>
          <p>今日消息: {{ stats.chats?.today_messages || 0 }}</p>
        </div>
        <div class="stat-card">
          <h3>订单统计</h3>
          <p>总订单: {{ stats.orders?.total || 0 }}</p>
          <p>总收入: ¥{{ stats.orders?.total_revenue || 0 }}</p>
          <p>今日收入: ¥{{ stats.orders?.today_revenue || 0 }}</p>
        </div>
      </div>
    </div>

    <!-- 用户管理 -->
    <div class="users-section">
      <h2>用户管理</h2>
      
      <!-- 搜索和分页控制 -->
      <div class="controls">
        <input 
          v-model="searchQuery" 
          @input="searchUsers"
          placeholder="搜索用户（邮箱或UUID）"
          class="search-input"
        />
        <select v-model="pageSize" @change="loadUsers">
          <option value="10">10条/页</option>
          <option value="20">20条/页</option>
          <option value="50">50条/页</option>
        </select>
      </div>

      <!-- 用户列表 -->
      <div class="user-list">
        <table>
          <thead>
            <tr>
              <th>UUID</th>
              <th>邮箱</th>
              <th>类型</th>
              <th>角色</th>
              <th>状态</th>
              <th>消息点数</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user.uuid">
              <td>{{ user.uuid.substring(0, 8) }}...</td>
              <td>{{ user.email || '游客' }}</td>
              <td>{{ user.is_guest ? '游客' : '注册' }}</td>
              <td>{{ user.role === 'admin' ? '管理员' : '用户' }}</td>
              <td>
                <span :class="['status', user.status]">
                  {{ user.status === 'active' ? '正常' : '禁用' }}
                </span>
              </td>
              <td>{{ user.message_credits }}</td>
              <td>{{ formatTime(user.created_at) }}</td>
              <td>
                <button @click="viewUser(user.uuid)" class="btn-view">查看</button>
                <button 
                  v-if="user.role !== 'admin'"
                  @click="toggleUserStatus(user)"
                  :class="['btn-toggle', user.status]"
                >
                  {{ user.status === 'active' ? '禁用' : '启用' }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button 
          @click="changePage(currentPage - 1)"
          :disabled="currentPage <= 1"
        >
          上一页
        </button>
        <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        <button 
          @click="changePage(currentPage + 1)"
          :disabled="currentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 用户详情弹窗 -->
    <div v-if="selectedUser" class="user-modal">
      <div class="modal-content">
        <h3>用户详情</h3>
        <div class="user-details">
          <p><strong>UUID:</strong> {{ selectedUser.uuid }}</p>
          <p><strong>邮箱:</strong> {{ selectedUser.email || '游客用户' }}</p>
          <p><strong>类型:</strong> {{ selectedUser.is_guest ? '游客' : '注册用户' }}</p>
          <p><strong>角色:</strong> {{ selectedUser.role === 'admin' ? '管理员' : '普通用户' }}</p>
          <p><strong>状态:</strong> {{ selectedUser.status === 'active' ? '正常' : '禁用' }}</p>
          <p><strong>今日消息:</strong> {{ selectedUser.daily_message_count }}</p>
          <p><strong>剩余点数:</strong> {{ selectedUser.message_credits }}</p>
          <p><strong>用户画像:</strong> {{ selectedUser.profile_summary || '暂无' }}</p>
          <p><strong>创建时间:</strong> {{ formatTime(selectedUser.created_at) }}</p>
          <p><strong>更新时间:</strong> {{ formatTime(selectedUser.updated_at) }}</p>
        </div>
        
        <div class="user-activities">
          <h4>聊天记录 ({{ userChats.length }})</h4>
          <div class="chat-list">
            <div v-for="chat in userChats" :key="chat.uuid" class="chat-item">
              <p><strong>{{ chat.title }}</strong></p>
              <p>AI角色: {{ chat.ai_role_name }}</p>
              <p>时间: {{ formatTime(chat.updated_at) }}</p>
            </div>
          </div>

          <h4>订单记录 ({{ userOrders.length }})</h4>
          <div class="order-list">
            <div v-for="order in userOrders" :key="order.uuid" class="order-item">
              <p><strong>{{ order.plan_name }}</strong> - ¥{{ order.amount }}</p>
              <p>状态: {{ getOrderStatusText(order.status) }}</p>
              <p>时间: {{ formatTime(order.created_at) }}</p>
            </div>
          </div>
        </div>

        <button @click="closeUserModal" class="btn-close">关闭</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      stats: {},
      users: [],
      currentPage: 1,
      pageSize: 20,
      totalPages: 1,
      searchQuery: '',
      selectedUser: null,
      userChats: [],
      userOrders: [],
      adminToken: localStorage.getItem('adminToken')
    }
  },

  async mounted() {
    if (!this.adminToken) {
      this.$router.push('/admin/login');
      return;
    }
    
    await this.loadStats();
    await this.loadUsers();
  },

  methods: {
    async loadStats() {
      try {
        const response = await fetch('/api/v1/admin/stats', {
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.stats = result.data;
        }
      } catch (error) {
        console.error('加载统计失败:', error);
      }
    },

    async loadUsers() {
      try {
        const params = new URLSearchParams({
          page: this.currentPage.toString(),
          limit: this.pageSize.toString()
        });
        
        if (this.searchQuery) {
          params.append('search', this.searchQuery);
        }

        const response = await fetch(`/api/v1/admin/users?${params}`, {
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });
        
        const result = await response.json();
        if (result.code === 200) {
          this.users = result.data.users;
          this.totalPages = result.data.pagination.pages;
        }
      } catch (error) {
        console.error('加载用户列表失败:', error);
      }
    },

    async viewUser(userUuid) {
      try {
        // 获取用户详情
        const userResponse = await fetch(`/api/v1/admin/users/${userUuid}`, {
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });
        const userResult = await userResponse.json();
        
        // 获取用户聊天记录
        const chatsResponse = await fetch(`/api/v1/admin/users/${userUuid}/chats`, {
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });
        const chatsResult = await chatsResponse.json();
        
        // 获取用户订单记录
        const ordersResponse = await fetch(`/api/v1/admin/users/${userUuid}/orders`, {
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });
        const ordersResult = await ordersResponse.json();

        if (userResult.code === 200) {
          this.selectedUser = userResult.data;
          this.userChats = chatsResult.code === 200 ? chatsResult.data : [];
          this.userOrders = ordersResult.code === 200 ? ordersResult.data : [];
        }
      } catch (error) {
        console.error('获取用户详情失败:', error);
      }
    },

    async toggleUserStatus(user) {
      if (user.role === 'admin') {
        alert('不能禁用管理员账户');
        return;
      }

      const newStatus = user.status === 'active' ? 'banned' : 'active';
      const action = newStatus === 'banned' ? '禁用' : '启用';
      
      if (!confirm(`确定要${action}用户 ${user.email || user.uuid} 吗？`)) {
        return;
      }

      try {
        const response = await fetch(`/api/v1/admin/users/${user.uuid}/status`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.adminToken}`
          },
          body: JSON.stringify({ status: newStatus })
        });

        const result = await response.json();
        if (result.code === 200) {
          user.status = newStatus;
          alert(`用户${action}成功`);
        } else {
          alert(`${action}失败: ${result.message}`);
        }
      } catch (error) {
        console.error(`${action}用户失败:`, error);
        alert(`${action}失败，请重试`);
      }
    },

    searchUsers() {
      this.currentPage = 1;
      this.loadUsers();
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
        this.loadUsers();
      }
    },

    closeUserModal() {
      this.selectedUser = null;
      this.userChats = [];
      this.userOrders = [];
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    },

    getOrderStatusText(status) {
      const statusMap = {
        pending: '待支付',
        completed: '已完成',
        failed: '支付失败',
        refunded: '已退款'
      };
      return statusMap[status] || status;
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.status.active {
  color: #28a745;
}

.status.banned {
  color: #dc3545;
}

.btn-view {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 5px;
}

.btn-toggle.active {
  background-color: #dc3545;
  color: white;
}

.btn-toggle.banned {
  background-color: #28a745;
  color: white;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.user-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  width: 90%;
}

.user-details p {
  margin: 10px 0;
}

.chat-item, .order-item {
  background: #f8f9fa;
  padding: 10px;
  margin: 5px 0;
  border-radius: 4px;
}
</style>
```

---

## 安全考虑

1. **权限验证**: 所有接口都需要管理员权限验证
2. **参数验证**: 严格的输入验证和格式检查
3. **操作限制**: 防止管理员账户被禁用
4. **审计日志**: 建议记录管理员操作日志
5. **密码安全**: 及时修改默认管理员密码

---

## 注意事项

1. **默认账户**: 系统会自动创建默认管理员账户，请及时修改密码
2. **权限控制**: 只有role为"admin"的用户才能访问管理接口
3. **数据安全**: 管理员可以查看所有用户数据，请谨慎操作
4. **状态更新**: 用户状态更新会立即生效，影响用户登录
5. **分页限制**: 每页最多返回100条记录，防止数据量过大

---

## 测试覆盖

模块六包含完整的测试覆盖：
- ✅ 系统统计信息获取测试
- ✅ 用户列表获取测试（分页、搜索）
- ✅ 用户详情获取测试
- ✅ 用户状态更新测试
- ✅ 用户聊天记录获取测试
- ✅ 用户订单记录获取测试
- ✅ 权限验证测试（管理员权限检查）
- ✅ 参数验证测试（UUID格式、状态值等）
- ✅ 错误处理测试（不存在的用户等）

所有15个测试用例均已通过验证，管理功能完整可用。
