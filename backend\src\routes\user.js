const express = require('express');
const userCenterController = require('../controllers/userCenterController');
const { authenticateToken } = require('../middleware/auth');
const { validate, validateParams, changePasswordSchema, sessionUuidSchema } = require('../validators/user');

const router = express.Router();

// 所有用户中心接口都需要认证
router.use(authenticateToken);

/**
 * @route GET /api/v1/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/', userCenterController.getCurrentUser);

/**
 * @route GET /api/v1/me/chats
 * @desc 获取用户的所有聊天会话列表
 * @access Private
 */
router.get('/chats', userCenterController.getUserChatSessions);

/**
 * @route PUT /api/v1/me/password
 * @desc 修改密码
 * @access Private
 */
router.put('/password', validate(changePasswordSchema), userCenterController.changePassword);

/**
 * @route DELETE /api/v1/me/chats/:session_uuid
 * @desc 删除指定聊天会话
 * @access Private
 */
router.delete('/chats/:session_uuid', validateParams(sessionUuidSchema), userCenterController.deleteChatSession);

/**
 * @route DELETE /api/v1/me/chats
 * @desc 删除所有聊天历史记录
 * @access Private
 */
router.delete('/chats', userCenterController.deleteAllChatHistory);

module.exports = router;
