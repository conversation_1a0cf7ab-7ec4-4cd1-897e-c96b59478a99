const express = require('express');
const userCenterController = require('../controllers/userCenterController');
const { authenticateToken } = require('../middleware/auth');
const { validate, changePasswordSchema } = require('../validators/user');

const router = express.Router();

// 所有用户中心接口都需要认证
router.use(authenticateToken);

/**
 * @route GET /api/v1/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/', userCenterController.getCurrentUser);

/**
 * @route GET /api/v1/me/chats
 * @desc 获取用户的所有聊天会话列表
 * @access Private
 */
router.get('/chats', userCenterController.getUserChatSessions);

/**
 * @route PUT /api/v1/me/password
 * @desc 修改密码
 * @access Private
 */
router.put('/password', validate(changePasswordSchema), userCenterController.changePassword);

module.exports = router;
