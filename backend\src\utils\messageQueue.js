const EventEmitter = require('events');

/**
 * 简单的内存消息队列实现
 * 生产环境建议使用Redis、RabbitMQ等专业消息队列
 */
class MessageQueue extends EventEmitter {
  constructor() {
    super();
    this.queues = new Map();
    this.workers = new Map();
    this.processing = new Map();
    this.retryAttempts = new Map();
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1秒
  }

  /**
   * 添加任务到队列
   * @param {string} queueName - 队列名称
   * @param {Object} task - 任务数据
   * @param {Object} options - 选项
   */
  enqueue(queueName, task, options = {}) {
    const {
      priority = 0,
      delay = 0,
      maxRetries = this.maxRetries
    } = options;

    const taskId = this.generateTaskId();
    const taskData = {
      id: taskId,
      queueName,
      data: task,
      priority,
      maxRetries,
      attempts: 0,
      createdAt: Date.now(),
      scheduledAt: Date.now() + delay
    };

    if (!this.queues.has(queueName)) {
      this.queues.set(queueName, []);
    }

    const queue = this.queues.get(queueName);
    
    // 按优先级插入（优先级高的在前）
    let insertIndex = queue.length;
    for (let i = 0; i < queue.length; i++) {
      if (queue[i].priority < priority) {
        insertIndex = i;
        break;
      }
    }
    
    queue.splice(insertIndex, 0, taskData);

    this.emit('taskEnqueued', { queueName, taskId, task: taskData });
    
    // 如果没有延迟，立即尝试处理
    if (delay === 0) {
      this.processQueue(queueName);
    } else {
      // 设置延迟处理
      setTimeout(() => {
        this.processQueue(queueName);
      }, delay);
    }

    return taskId;
  }

  /**
   * 注册队列处理器
   * @param {string} queueName - 队列名称
   * @param {Function} processor - 处理函数
   * @param {Object} options - 选项
   */
  process(queueName, processor, options = {}) {
    const {
      concurrency = 1,
      timeout = 30000 // 30秒超时
    } = options;

    this.workers.set(queueName, {
      processor,
      concurrency,
      timeout,
      activeJobs: 0
    });

    // 立即开始处理队列
    this.processQueue(queueName);
  }

  /**
   * 处理队列中的任务
   * @param {string} queueName - 队列名称
   */
  async processQueue(queueName) {
    const worker = this.workers.get(queueName);
    if (!worker) {
      return; // 没有注册处理器
    }

    const queue = this.queues.get(queueName);
    if (!queue || queue.length === 0) {
      return; // 队列为空
    }

    // 检查并发限制
    if (worker.activeJobs >= worker.concurrency) {
      return; // 已达到并发限制
    }

    // 获取下一个可处理的任务
    const now = Date.now();
    const taskIndex = queue.findIndex(task => 
      task.scheduledAt <= now && 
      !this.processing.has(task.id)
    );

    if (taskIndex === -1) {
      return; // 没有可处理的任务
    }

    const task = queue.splice(taskIndex, 1)[0];
    this.processing.set(task.id, task);
    worker.activeJobs++;

    try {
      // 设置超时
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Task timeout')), worker.timeout);
      });

      // 执行任务
      const processingPromise = worker.processor(task.data, task);
      
      await Promise.race([processingPromise, timeoutPromise]);

      // 任务成功完成
      this.processing.delete(task.id);
      this.retryAttempts.delete(task.id);
      worker.activeJobs--;

      this.emit('taskCompleted', { queueName, taskId: task.id, task });

    } catch (error) {
      // 任务失败
      this.processing.delete(task.id);
      worker.activeJobs--;
      task.attempts++;

      this.emit('taskFailed', { queueName, taskId: task.id, task, error });

      // 检查是否需要重试
      if (task.attempts < task.maxRetries) {
        // 重新入队，增加延迟
        const retryDelay = this.retryDelay * Math.pow(2, task.attempts - 1); // 指数退避
        task.scheduledAt = Date.now() + retryDelay;
        
        queue.push(task);
        this.emit('taskRetry', { queueName, taskId: task.id, task, attempt: task.attempts });

        // 延迟后重新处理
        setTimeout(() => {
          this.processQueue(queueName);
        }, retryDelay);
      } else {
        // 重试次数用完，任务彻底失败
        this.emit('taskExhausted', { queueName, taskId: task.id, task, error });
      }
    }

    // 继续处理队列中的其他任务
    setImmediate(() => {
      this.processQueue(queueName);
    });
  }

  /**
   * 获取队列状态
   * @param {string} queueName - 队列名称
   * @returns {Object} 队列状态
   */
  getQueueStatus(queueName) {
    const queue = this.queues.get(queueName) || [];
    const worker = this.workers.get(queueName);
    
    const waiting = queue.filter(task => !this.processing.has(task.id)).length;
    const processing = Array.from(this.processing.values())
      .filter(task => task.queueName === queueName).length;

    return {
      name: queueName,
      waiting,
      processing,
      total: waiting + processing,
      hasWorker: !!worker,
      concurrency: worker?.concurrency || 0,
      activeJobs: worker?.activeJobs || 0
    };
  }

  /**
   * 获取所有队列状态
   * @returns {Array} 所有队列状态
   */
  getAllQueuesStatus() {
    const allQueues = new Set([
      ...this.queues.keys(),
      ...this.workers.keys()
    ]);

    return Array.from(allQueues).map(queueName => 
      this.getQueueStatus(queueName)
    );
  }

  /**
   * 清空队列
   * @param {string} queueName - 队列名称
   */
  clearQueue(queueName) {
    if (this.queues.has(queueName)) {
      this.queues.set(queueName, []);
    }
    
    // 清理正在处理的任务
    for (const [taskId, task] of this.processing.entries()) {
      if (task.queueName === queueName) {
        this.processing.delete(taskId);
      }
    }

    this.emit('queueCleared', { queueName });
  }

  /**
   * 生成任务ID
   * @returns {string} 任务ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 优雅关闭
   * @param {number} timeout - 超时时间（毫秒）
   */
  async shutdown(timeout = 30000) {
    console.log('开始关闭消息队列...');

    const startTime = Date.now();
    
    // 等待所有正在处理的任务完成
    while (this.processing.size > 0 && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    if (this.processing.size > 0) {
      console.warn(`强制关闭，仍有 ${this.processing.size} 个任务在处理中`);
    }

    // 清理所有队列
    this.queues.clear();
    this.workers.clear();
    this.processing.clear();
    this.retryAttempts.clear();

    console.log('消息队列已关闭');
  }
}

// 创建全局队列实例
const globalQueue = new MessageQueue();

// 注册常用队列处理器

/**
 * 支付回调处理队列
 */
globalQueue.process('payment-callback', async (data, task) => {
  const { paymentService } = require('../services/paymentService');
  
  console.log(`处理支付回调: ${data.orderNumber}`);
  
  try {
    await paymentService.handlePaymentCallback(data);
    console.log(`支付回调处理成功: ${data.orderNumber}`);
  } catch (error) {
    console.error(`支付回调处理失败: ${data.orderNumber}`, error);
    throw error;
  }
}, { concurrency: 2, timeout: 10000 });

/**
 * 邮件发送队列
 */
globalQueue.process('email', async (data, task) => {
  console.log(`发送邮件: ${data.to} - ${data.subject}`);
  
  // 这里集成实际的邮件服务
  // 例如：SendGrid、阿里云邮件推送等
  
  // 模拟邮件发送
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log(`邮件发送成功: ${data.to}`);
}, { concurrency: 3, timeout: 15000 });

/**
 * 数据统计队列
 */
globalQueue.process('statistics', async (data, task) => {
  console.log(`处理统计任务: ${data.type}`);
  
  // 这里处理各种统计任务
  // 例如：用户活跃度统计、消息使用统计等
  
  await new Promise(resolve => setTimeout(resolve, 500));
  
  console.log(`统计任务完成: ${data.type}`);
}, { concurrency: 1, timeout: 30000 });

// 监听队列事件
globalQueue.on('taskCompleted', ({ queueName, taskId }) => {
  console.log(`任务完成: ${queueName}/${taskId}`);
});

globalQueue.on('taskFailed', ({ queueName, taskId, error }) => {
  console.error(`任务失败: ${queueName}/${taskId}`, error.message);
});

globalQueue.on('taskExhausted', ({ queueName, taskId, error }) => {
  console.error(`任务彻底失败: ${queueName}/${taskId}`, error.message);
  // 这里可以发送告警通知
});

module.exports = {
  MessageQueue,
  queue: globalQueue
};
