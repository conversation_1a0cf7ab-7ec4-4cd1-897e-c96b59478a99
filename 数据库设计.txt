数据库信息:localhost 3306端口 账号root 密码root, 请你用语句创建库创建表。
### **核心数据实体识别**

根据您的功能清单和架构文档，我识别出以下核心业务实体：

1.  **用户 (Users):** 系统核心实体，包含游客和注册用户。
2.  **AI 角色 (AI Roles):** 定义了不同 AI 伙伴的人设和属性。
3.  **聊天会话 (Chat Sessions):** 唯一标识一次用户与一个AI角色的完整对话。
4.  **聊天消息 (Chat Messages):** 记录会话中的每一条具体消息。
5.  **套餐计划 (Plans):** 商业化模块中的付费套餐定义。
6.  **订单 (Orders):** 记录用户的支付行为。
7.  **用户订阅 (User Subscriptions):** 记录用户的会员订阅状态和有效期。

接下来，我将按照您的模块划分，逐一展示相关的表结构设计。

-----

### **模块一：用户认证与账户基础**

此模块是整个系统的基石，核心是 `users` 表的设计。该表需要同时支持临时游客和正式注册用户，并为未来的功能（如使用额度、用户画像）预留字段。

#### **表结构设计: `users`**

**说明:** 该表用于存储所有用户，通过 `is_guest` 字段区分游客和正式用户。预置了 `status` 字段用于后台管理，`daily_message_count` 和 `last_usage_date` 用于实现免费额度限制，`message_credits` 用于存储购买的消息包额度，`profile_summary` 用于实现“长期记忆”亮点功能。

| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 内部主键，无符号大整型，应对海量用户 |
| `uuid` | `CHAR(36)` | `NOT NULL`, `UNIQUE` | 对外暴露的唯一ID，避免泄露自增主键 |
| `email` | `VARCHAR(255)` | `UNIQUE` | 邮箱，注册用户唯一，游客为NULL |
| `password_hash` | `VARCHAR(255)` | - | 加盐哈希后的密码，游客为NULL |
| `is_guest` | `BOOLEAN` | `NOT NULL`, `DEFAULT TRUE` | 是否为游客，`TRUE`=是, `FALSE`=否 |
| `status` | `ENUM('active', 'banned')` | `NOT NULL`, `DEFAULT 'active'` | 账户状态，用于后台管理 |
| `daily_message_count` | `INT UNSIGNED` | `NOT NULL`, `DEFAULT 0` | 当日已发送消息计数 |
| `last_usage_date` | `DATE` | - | 上次使用日期，用于重置每日计数 |
| `message_credits` | `INT` | `NOT NULL`, `DEFAULT 0` | 用户购买的剩余消息条数 |
| `profile_summary` | `TEXT` | - | (亮点功能) AI生成的长期记忆摘要 |
| `created_at` | `TIMESTAMP` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` | 创建时间 |
| `updated_at` | `TIMESTAMP` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 最后更新时间 |

#### **CREATE TABLE 语句**

```sql
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户内部ID',
  `uuid` CHAR(36) NOT NULL COMMENT '用户公开ID',
  `email` VARCHAR(255) NULL COMMENT '邮箱',
  `password_hash` VARCHAR(255) NULL COMMENT '加盐哈希后的密码',
  `is_guest` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否为游客账户',
  `status` ENUM('active', 'banned') NOT NULL DEFAULT 'active' COMMENT '账户状态 (active, banned)',
  `daily_message_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日消息使用计数',
  `last_usage_date` DATE NULL COMMENT '上次使用日期，用于重置每日计数',
  `message_credits` INT NOT NULL DEFAULT 0 COMMENT '剩余消息点数/条数',
  `profile_summary` TEXT NULL COMMENT '用户长期记忆摘要',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

#### **索引建议**

  * `PRIMARY KEY (id)`: 数据库自动创建，用于内部关联。
  * `UNIQUE KEY uk_uuid (uuid)`: 保证公开ID的唯一性，并用于通过UUID快速查询用户。
  * `UNIQUE KEY uk_email (email)`: 保证邮箱的唯一性，并加速登录和注册时的邮箱校验。

-----

### **模块二：AI 角色展示与选择**

此模块的核心是 `ai_roles` 表，用于存储和管理所有可供用户选择的 AI 伙伴信息。运营人员可以通过后台管理此表，动态调整 AI 角色。

#### **表结构设计: `ai_roles`**

**说明:** 存储 AI 角色的所有配置信息。`system_prompt` 是核心字段，直接决定了 AI 的人设和行为。

| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 角色主键 |
| `name` | `VARCHAR(100)` | `NOT NULL` | 角色名称 |
| `avatar_url` | `VARCHAR(255)` | - | 角色头像图片的URL |
| `description` | `VARCHAR(500)` | - | 角色的简短介绍（卡片上显示） |
| `details` | `TEXT` | - | 角色的详细背景故事和设定 |
| `system_prompt` | `TEXT` | `NOT NULL` | 核心人设指令，用于与AI API交互 |
| `is_active` | `BOOLEAN` | `NOT NULL`, `DEFAULT TRUE` | 是否启用，用于后台控制上下线 |
| `created_at` | `TIMESTAMP` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` | 创建时间 |
| `updated_at` | `TIMESTAMP` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 最后更新时间 |

#### **CREATE TABLE 语句**

```sql
CREATE TABLE `ai_roles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'AI角色ID',
  `name` VARCHAR(100) NOT NULL COMMENT '角色名称',
  `avatar_url` VARCHAR(255) NULL COMMENT '角色头像URL',
  `description` VARCHAR(500) NULL COMMENT '角色一句话简介',
  `details` TEXT NULL COMMENT '角色详细介绍',
  `system_prompt` TEXT NOT NULL COMMENT '给AI的核心人设指令(System Prompt)',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用该角色',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色配置表';
```

#### **索引建议**

  * `PRIMARY KEY (id)`: 默认创建。由于角色数量预计不会非常巨大，主键索引已基本满足查询需求。

-----

### **模块三：核心对话功能**

这是产品的核心模块，涉及两个关键表：`chat_sessions` 用于组织对话，`chat_messages` 用于记录具体消息。

#### **表间关系**

  * `users` (1) -\> (N) `chat_sessions`: 一个用户可以有多个聊天会话。
  * `ai_roles` (1) -\> (N) `chat_sessions`: 一个AI角色可以被多个会话使用。
  * `chat_sessions` (1) -\> (N) `chat_messages`: 一个会话包含多条聊天消息。

#### **表结构设计: `chat_sessions`**

**说明:** 该表将用户、AI角色和一次完整的对话关联起来。每次用户选择一个新的AI角色开始聊天时，都应创建一条新的会话记录。`updated_at` 字段对于在历史记录中排序至关重要。

| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 会话内部主键 |
| `uuid` | `CHAR(36)` | `NOT NULL`, `UNIQUE` | 对外暴露的会话ID |
| `user_id` | `BIGINT UNSIGNED` | `NOT NULL` | 关联到 `users.id` |
| `ai_role_id` | `INT UNSIGNED` | `NOT NULL` | 关联到 `ai_roles.id` |
| `title` | `VARCHAR(255)` | - | 会话标题（可由首条消息生成） |
| `created_at` | `TIMESTAMP` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP` | 创建时间 |
| `updated_at` | `TIMESTAMP` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP` | 最后消息时间，用于排序 |

#### **表结构设计: `chat_messages`**

**说明:** 记录每一次对话的具体内容。`role` 字段区分消息来源，`emotion` 字段用于实现“动态情绪分析”亮点功能。

| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | `PRIMARY KEY`, `AUTO_INCREMENT` | 消息主键 |
| `session_id` | `BIGINT UNSIGNED` | `NOT NULL` | 关联到 `chat_sessions.id` |
| `role` | `ENUM('user', 'assistant')` | `NOT NULL` | 消息发送方 (`user`或`assistant`) |
| `content` | `TEXT` | `NOT NULL` | 消息的具体文本内容 |
| `emotion` | `VARCHAR(50)` | - | (亮点功能) NLP分析出的用户情绪 |
| `created_at` | `TIMESTAMP(3)` | `NOT NULL`, `DEFAULT CURRENT_TIMESTAMP(3)` | 创建时间，使用毫秒精度保证顺序 |

#### **CREATE TABLE 语句**

```sql
CREATE TABLE `chat_sessions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '会话内部ID',
  `uuid` CHAR(36) NOT NULL COMMENT '会话公开ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的用户ID',
  `ai_role_id` INT UNSIGNED NOT NULL COMMENT '关联的AI角色ID',
  `title` VARCHAR(255) NULL COMMENT '会话标题',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`),
  KEY `idx_user_updated` (`user_id`, `updated_at`),
  CONSTRAINT `fk_sessions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sessions_role` FOREIGN KEY (`ai_role_id`) REFERENCES `ai_roles` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

CREATE TABLE `chat_messages` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `session_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的会话ID',
  `role` ENUM('user', 'assistant') NOT NULL COMMENT '消息发送方',
  `content` TEXT NOT NULL COMMENT '消息内容',
  `emotion` VARCHAR(50) NULL COMMENT '分析出的用户情绪',
  `created_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间(毫秒精度)',
  PRIMARY KEY (`id`),
  KEY `idx_session_created` (`session_id`, `created_at`),
  CONSTRAINT `fk_messages_session` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息记录表';
```

#### **索引建议**

  * `chat_sessions`:
      * `idx_user_updated (user_id, updated_at)`: **复合索引**，极大地优化“查询某用户的历史会话列表并按时间倒序排列”这一核心场景。
  * `chat_messages`:
      * `idx_session_created (session_id, created_at)`: **复合索引**，用于高效地获取某个会话的所有消息，并保证其时间顺序，是对话上下文管理和历史记录查看的关键。

-----

### **模块四：用户中心**

此模块的功能主要依赖于对现有表的查询，不产生新的数据表结构。

  * **对话历史列表:** 通过 `user_id` 查询 `chat_sessions` 表，并根据 `updated_at` 排序。
  * **历史对话查看:** 通过 `session_id` 查询 `chat_messages` 表。
  * **账户设置:** 对 `users` 表进行更新操作。

**涉及的表 (已在前面模块定义):**

  * `users`
  * `chat_sessions`
  * `chat_messages`

-----

### **模块五：支付与充值**

此模块引入了商业化所需的表结构，包括套餐、订单和订阅。

#### **表间关系**

  * `users` (1) -\> (N) `orders`: 一个用户可以有多个订单。
  * `plans` (1) -\> (N) `orders`: 一个套餐可以对应多个订单。
  * `users` (1) -\> (N) `user_subscriptions`: 一个用户可以有多个订阅记录。
  * `plans` (1) -\> (N) `user_subscriptions`: 一个套餐计划可以被多个用户订阅。
  * `orders` (1) -\> (1) `user_subscriptions`: 一个成功的订阅订单会生成一条订阅记录。

#### **表结构设计: `plans`, `orders`, `user_subscriptions`**

  * **`plans`**: 定义所有可售卖的商品或服务。
  * **`orders`**: 记录每一笔支付请求，包含支付网关信息，是财务对账的基础。
  * **`user_subscriptions`**: 专门管理用户的会员订阅状态，与 `orders` 解耦，清晰地记录用户权益的有效期。

#### **CREATE TABLE 语句**

```sql
CREATE TABLE `plans` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套餐ID',
  `name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
  `description` VARCHAR(255) NULL COMMENT '套餐描述',
  `price` DECIMAL(10, 2) NOT NULL COMMENT '价格',
  `plan_type` ENUM('subscription', 'one_time') NOT NULL COMMENT '类型: subscription-订阅, one_time-一次性',
  `message_credits` INT NULL COMMENT '对于one_time类型, 包含的消息条数',
  `duration_days` INT NULL COMMENT '对于subscription类型, 有效期天数',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用该套餐',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品套餐表';

CREATE TABLE `orders` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `uuid` CHAR(36) NOT NULL COMMENT '订单公开ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的用户ID',
  `plan_id` INT UNSIGNED NOT NULL COMMENT '关联的套餐ID',
  `amount` DECIMAL(10, 2) NOT NULL COMMENT '订单金额',
  `status` ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_gateway` VARCHAR(50) NULL COMMENT '支付网关 (e.g., alipay, wechat_pay)',
  `gateway_transaction_id` VARCHAR(255) NULL COMMENT '支付网关返回的交易号',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uuid` (`uuid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_gateway_tid` (`gateway_transaction_id`),
  CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_orders_plan` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付订单表';

CREATE TABLE `user_subscriptions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订阅记录ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的用户ID',
  `plan_id` INT UNSIGNED NOT NULL COMMENT '关联的套餐ID',
  `order_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的订单ID',
  `start_date` TIMESTAMP NOT NULL COMMENT '订阅开始时间',
  `end_date` TIMESTAMP NOT NULL COMMENT '订阅结束时间',
  `status` ENUM('active', 'expired', 'cancelled') NOT NULL DEFAULT 'active' COMMENT '订阅状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_user_end_date` (`user_id`, `end_date`, `status`),
  CONSTRAINT `fk_subs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_subs_plan` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_subs_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅记录表';
```

#### **索引建议**

  * `orders`:
      * `idx_user_id (user_id)`: 方便查询某用户的所有订单。
      * `idx_gateway_tid (gateway_transaction_id)`: 方便通过支付平台返回的ID快速定位订单，用于处理异步回调。
  * `user_subscriptions`:
      * `idx_user_end_date (user_id, end_date, status)`: **复合索引**，核心索引之一。用于快速检查某个用户的当前有效订阅状态。

-----

### **模块六：后台管理系统**

此模块是为运营人员提供管理后台，所有功能都是对现有表的增删改查（CRUD）。

**涉及的表 (已在前面模块定义):**

  * `users`: 用于用户管理。
  * `ai_roles`: 用于 AI 角色管理。
  * `orders`: 用于订单管理。
  * `plans`: (可能需要)用于套餐管理。

无需新建表，只需基于以上表结构开发对应的后台管理界面和API即可。
