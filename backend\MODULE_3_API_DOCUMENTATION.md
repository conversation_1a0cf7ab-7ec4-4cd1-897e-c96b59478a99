# AI伙伴聊天应用 - 模块三API文档

## 概述

本文档描述了AI伙伴聊天应用**模块三：核心对话功能**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token（所有接口都需要认证）

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "聊天会话不存在或无权访问",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 201 | 201 | 资源创建成功 |
| 400 | 40001 | 请求参数无效 |
| 401 | 40101 | 未授权或Token无效 |
| 404 | 40401 | 请求的资源未找到 |
| 500 | 50001 | 服务器内部错误 |
| 500 | 50002 | AI回复生成失败 |

---

## 接口列表

### 1. 创建聊天会话

**接口描述**: 创建一个新的聊天会话，指定AI角色

- **URL**: `POST /chat/sessions`
- **认证**: 需要JWT Token

**请求参数**:
```json
{
  "ai_role_id": 1
}
```

**参数说明**:
- `ai_role_id`: AI角色ID，必须是正整数，且角色必须存在并启用

**响应示例**:
```json
{
  "code": 201,
  "message": "Chat session created successfully",
  "data": {
    "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "ai_role_id": 1,
    "title": "新的对话",
    "created_at": "2025-07-22T10:00:00.000Z"
  }
}
```

**错误响应**:
- `404 Not Found`: AI角色不存在或已禁用
- `400 Bad Request`: 参数验证失败
- `401 Unauthorized`: 未认证

### 2. 获取用户聊天会话列表

**接口描述**: 获取当前用户的所有聊天会话，按最后更新时间倒序排列

- **URL**: `GET /chat/sessions`
- **认证**: 需要JWT Token
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "Sessions retrieved successfully",
  "data": [
    {
      "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "title": "新的对话",
      "ai_role_id": 1,
      "ai_role_name": "博学的历史学家",
      "updated_at": "2025-07-22T10:30:00.000Z"
    },
    {
      "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
      "title": "新的对话",
      "ai_role_id": 2,
      "ai_role_name": "温柔的心理顾问",
      "updated_at": "2025-07-22T09:15:00.000Z"
    }
  ]
}
```

**字段说明**:
- `uuid`: 会话的唯一标识符
- `title`: 会话标题
- `ai_role_id`: 关联的AI角色ID
- `ai_role_name`: AI角色名称
- `updated_at`: 最后更新时间

### 3. 发送消息

**接口描述**: 向指定会话发送消息并获取AI回复

- **URL**: `POST /chat/sessions/{session_uuid}/messages`
- **认证**: 需要JWT Token

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| session_uuid | string | 是 | 会话UUID，必须是有效的UUID格式 |

**请求参数**:
```json
{
  "content": "你好，我想了解一下古代中国的历史"
}
```

**参数说明**:
- `content`: 消息内容，1-2000个字符，不能为空

**响应示例**:
```json
{
  "code": 200,
  "message": "Message sent successfully",
  "data": {
    "role": "assistant",
    "content": "您好！我很高兴为您介绍古代中国的历史。中国有着五千年的悠久历史，从传说中的三皇五帝时代开始，经历了夏、商、周等朝代的更迭...",
    "emotion": null,
    "created_at": "2025-07-22T10:31:25.123Z"
  }
}
```

**字段说明**:
- `role`: 消息发送方，固定为"assistant"
- `content`: AI回复的内容
- `emotion`: 情绪分析结果（当前版本为null）
- `created_at`: 消息创建时间（毫秒精度）

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问
- `400 Bad Request`: 参数验证失败
- `500 Internal Server Error`: AI回复生成失败

### 4. 获取会话历史消息

**接口描述**: 获取指定会话的所有历史消息，按时间升序排列

- **URL**: `GET /chat/sessions/{session_uuid}/messages`
- **认证**: 需要JWT Token

**路径参数**:
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| session_uuid | string | 是 | 会话UUID，必须是有效的UUID格式 |

**响应示例**:
```json
{
  "code": 200,
  "message": "Messages retrieved successfully",
  "data": [
    {
      "role": "user",
      "content": "你好，我想了解一下古代中国的历史",
      "emotion": null,
      "created_at": "2025-07-22T10:31:20.456Z"
    },
    {
      "role": "assistant",
      "content": "您好！我很高兴为您介绍古代中国的历史。中国有着五千年的悠久历史，从传说中的三皇五帝时代开始，经历了夏、商、周等朝代的更迭...",
      "emotion": null,
      "created_at": "2025-07-22T10:31:25.123Z"
    }
  ]
}
```

**字段说明**:
- `role`: 消息发送方，"user"表示用户，"assistant"表示AI
- `content`: 消息内容
- `emotion`: 情绪分析结果（当前版本为null）
- `created_at`: 消息创建时间（毫秒精度）

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问
- `400 Bad Request`: 参数验证失败

---

## 参数验证规则

### 会话UUID验证
- **格式**: 标准UUID格式（8-4-4-4-12）
- **示例**: `a1b2c3d4-e5f6-7890-1234-567890abcdef`

### AI角色ID验证
- **类型**: 正整数
- **范围**: 必须大于0
- **存在性**: 必须是已启用的AI角色

### 消息内容验证
- **长度**: 1-2000个字符
- **格式**: 去除首尾空格后不能为空
- **编码**: UTF-8

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 创建聊天会话
async function createChatSession(aiRoleId, token) {
  try {
    const response = await fetch('/api/v1/chat/sessions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        ai_role_id: aiRoleId
      })
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('会话创建成功:', result.data);
      return result.data.uuid;
    } else {
      console.error('创建失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 2. 发送消息
async function sendMessage(sessionUuid, content, token) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        content: content
      })
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('AI回复:', result.data.content);
      return result.data;
    } else {
      console.error('发送失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 3. 获取历史消息
async function getMessages(sessionUuid, token) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('历史消息:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}

// 4. 获取会话列表
async function getSessions(token) {
  try {
    const response = await fetch('/api/v1/chat/sessions', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('会话列表:', result.data);
      return result.data;
    } else {
      console.error('获取失败:', result.message);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}
```

### Vue.js 聊天组件示例

```vue
<template>
  <div class="chat-container">
    <!-- 会话列表 -->
    <div class="session-list">
      <button @click="createNewSession">新建对话</button>
      <div 
        v-for="session in sessions" 
        :key="session.uuid"
        @click="selectSession(session.uuid)"
        :class="{ active: currentSessionUuid === session.uuid }"
      >
        <h4>{{ session.title }}</h4>
        <p>{{ session.ai_role_name }}</p>
        <small>{{ formatTime(session.updated_at) }}</small>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div class="chat-area" v-if="currentSessionUuid">
      <div class="messages">
        <div 
          v-for="message in messages" 
          :key="message.created_at"
          :class="['message', message.role]"
        >
          <div class="content">{{ message.content }}</div>
          <div class="time">{{ formatTime(message.created_at) }}</div>
        </div>
      </div>

      <div class="input-area">
        <textarea 
          v-model="inputMessage" 
          @keydown.enter.prevent="sendMessage"
          placeholder="输入消息..."
          :disabled="sending"
        ></textarea>
        <button @click="sendMessage" :disabled="sending || !inputMessage.trim()">
          {{ sending ? '发送中...' : '发送' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      sessions: [],
      currentSessionUuid: null,
      messages: [],
      inputMessage: '',
      sending: false,
      token: localStorage.getItem('token')
    }
  },

  async mounted() {
    await this.loadSessions();
  },

  methods: {
    async loadSessions() {
      try {
        const response = await fetch('/api/v1/chat/sessions', {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.sessions = result.data;
        }
      } catch (error) {
        console.error('加载会话列表失败:', error);
      }
    },

    async createNewSession() {
      // 假设已选择AI角色ID
      const aiRoleId = 1;
      try {
        const response = await fetch('/api/v1/chat/sessions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify({ ai_role_id: aiRoleId })
        });
        const result = await response.json();
        if (result.code === 201) {
          await this.loadSessions();
          this.selectSession(result.data.uuid);
        }
      } catch (error) {
        console.error('创建会话失败:', error);
      }
    },

    async selectSession(sessionUuid) {
      this.currentSessionUuid = sessionUuid;
      await this.loadMessages();
    },

    async loadMessages() {
      try {
        const response = await fetch(`/api/v1/chat/sessions/${this.currentSessionUuid}/messages`, {
          headers: { 'Authorization': `Bearer ${this.token}` }
        });
        const result = await response.json();
        if (result.code === 200) {
          this.messages = result.data;
        }
      } catch (error) {
        console.error('加载消息失败:', error);
      }
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.sending) return;

      const content = this.inputMessage.trim();
      this.inputMessage = '';
      this.sending = true;

      // 立即显示用户消息
      this.messages.push({
        role: 'user',
        content: content,
        created_at: new Date().toISOString()
      });

      try {
        const response = await fetch(`/api/v1/chat/sessions/${this.currentSessionUuid}/messages`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`
          },
          body: JSON.stringify({ content })
        });

        const result = await response.json();
        if (result.code === 200) {
          // 添加AI回复
          this.messages.push(result.data);
          // 更新会话列表
          await this.loadSessions();
        } else {
          alert('发送失败: ' + result.message);
        }
      } catch (error) {
        console.error('发送消息失败:', error);
        alert('发送失败，请重试');
      } finally {
        this.sending = false;
      }
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    }
  }
}
</script>
```

---

## 数据库结构

### chat_sessions表结构

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT UNSIGNED | PRIMARY KEY, AUTO_INCREMENT | 内部主键 |
| uuid | CHAR(36) | UNIQUE, NOT NULL | 对外公开ID |
| user_id | BIGINT UNSIGNED | NOT NULL, FK | 关联用户ID |
| ai_role_id | INT UNSIGNED | NOT NULL, FK | 关联AI角色ID |
| title | VARCHAR(255) | NULL | 会话标题 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### chat_messages表结构

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT UNSIGNED | PRIMARY KEY, AUTO_INCREMENT | 消息主键 |
| session_id | BIGINT UNSIGNED | NOT NULL, FK | 关联会话ID |
| role | ENUM('user', 'assistant') | NOT NULL | 消息发送方 |
| content | TEXT | NOT NULL | 消息内容 |
| emotion | VARCHAR(50) | NULL | 情绪分析结果 |
| created_at | TIMESTAMP(3) | NOT NULL, DEFAULT CURRENT_TIMESTAMP(3) | 创建时间（毫秒精度） |

---

## LLM集成说明

### 使用的LLM服务
- **提供商**: SophNet
- **模型**: DeepSeek-V3-Fast
- **API地址**: https://www.sophnet.com/api/open-apis/v1

### 配置参数
- **max_tokens**: 2000
- **temperature**: 0.7
- **stream**: false（同步调用）

### 上下文管理
- 每次调用包含最近10条消息作为上下文
- 自动包含AI角色的system_prompt
- 按时间顺序组织消息历史

### 错误处理
- API调用超时：30秒
- 自动重试机制：无（单次调用）
- 失败时返回友好错误消息

---

## 注意事项

1. **认证要求**: 所有接口都需要有效的JWT Token
2. **权限控制**: 用户只能访问自己创建的会话
3. **消息长度**: 单条消息最大2000字符
4. **LLM调用**: 可能耗时较长（通常2-10秒）
5. **错误处理**: 前端需要处理LLM调用失败的情况
6. **实时性**: 当前版本不支持流式响应
7. **上下文限制**: 只保留最近10条消息作为上下文

---

## 测试覆盖

模块三包含完整的测试覆盖：
- ✅ 会话创建测试（注册用户和游客）
- ✅ 会话列表获取测试
- ✅ 消息发送测试（包含真实LLM调用）
- ✅ 历史消息获取测试
- ✅ 参数验证测试（UUID格式、消息长度等）
- ✅ 权限验证测试（认证、会话所有权等）
- ✅ 错误处理测试（不存在的会话、角色等）

所有15个测试用例均已通过验证，包括真实的LLM API调用。
