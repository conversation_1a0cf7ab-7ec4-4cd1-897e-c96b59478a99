{"designSystem": {"visual": {"colorPalette": {"primary": "整体为深色模式（Dark Mode）。背景采用近乎纯黑的木炭灰色（#121212 - #1A1A1A），营造沉浸、私密的氛围。主要文字内容使用纯白色（#FFFFFF）或高亮度的浅灰色（#E0E0E0）。", "secondary": "次要信息、未激活的元素和UI控件背景（如对方的聊天气泡）使用中度灰色（#3A3A3A - #4F4F4F）。", "accent": "强调色采用双色策略：1. 功能性强调色（CTA）：使用明亮、充满活力的黄色（#FFD60A），用于“确定”、“发送”等核心操作按钮，确保视觉焦点。 2. 品牌/情感化强调色：使用带有霓虹光感的神秘紫色（#9B59B6 - #BE93FD），用于图标、装饰性元素（如药水瓶、星光点缀）和情感化反馈，增强产品的“树洞”和“魔法”感。", "gradients": "背景使用从深灰到近黑的极其微妙的径向渐变，增加深邃感。紫色装饰元素上使用同色系渐变，并叠加外发光效果，营造体积感和梦幻感。", "transparency": "广泛使用玻璃拟态（Glassmorphism）效果。模态对话框（Modal）、浮动面板等采用带有背景模糊（`backdrop-filter: blur(18px)`）的半透明白色（`rgba(255, 255, 255, 0.1)`）作为背景，并带有精细的1px白色边框（`rgba(255, 255, 255, 0.2)`）。", "contrast": "遵循WCAG AA标准，确保白色/黄色文字在深色背景上的高对比度，保证可读性。UI元素间的对比度则保持微妙，通过阴影和细微的灰度差异来区分层次。"}, "typography": {"fontFamily": "优先使用系统默认的现代无衬线中文字体，以保证最佳性能和可读性。具体为：苹果设备上的 PingFang SC，安卓设备上的 Noto Sans SC (思源黑体)。英文字体搭配 Inter 或 SF Pro。", "fontSizes": "采用基于4px的响应式字号系统：12px (辅助/时间戳), 14px (正文/聊天内容), 16px (标题/按钮文字), 18px (弹窗标题), 24px (页面大标题), 32px (营销/Slogan)。使用`clamp()`函数实现流畅的缩放。", "fontWeights": "常规 (Regular/400) 用于正文内容。中等 (Medium/500) 用于按钮文字和列表项标题。半粗 (Semibold/600) 用于页面和弹窗标题，营造清晰的视觉层次。", "lineHeight": "正文内容（聊天消息）采用1.6的行高，确保长段落的可读性。标题类采用1.3的行高，显得更紧凑有力。", "letterSpacing": "大字号标题（24px以上）应用微调的负字间距（-0.02em），使其视觉上更紧凑、更具冲击力。正文则使用默认字间距。", "textHierarchy": "严格遵循字号、字重和颜色的组合来构建信息层次。例如：页面标题（24px/600/白色） > 弹窗标题（18px/600/白色） > 聊天内容（14px/400/浅灰色） > 时间戳（12px/400/中灰色）。", "readability": "在深色背景下，纯白文字（#FFF）用于标题，而长篇正文使用略微柔和的浅灰色（#E0E0E0）以降低长时间阅读的视觉疲劳。"}, "spacing": {"baseUnit": "8px。所有布局间距、内边距都是8px的倍数，确保界面节奏感和一致性。", "scale": "8px, 16px, 24px, 32px, 48px, 64px。小间距用于组件内部，大间距用于模块和区块之间。", "whitespace": "大胆留白。聊天气泡之间、列表项之间、页面边缘都保留充足的间距，创造一个轻松、不压抑的视觉环境。", "contentWidth": "在PC Web端，内容最大宽度限制在768px，以保证聊天界面和列表的最佳阅读行长。", "sectionPadding": "页面主体内容的垂直和水平内边距通常为24px。卡片和弹窗的内部边距为16px或24px。", "componentSpacing": "按钮内联边距为 16px 水平 x 12px 垂直。输入框内边距为 12px。聊天气泡内边距为 12px。"}, "layout": {"gridSystem": "移动端主要采用基于Flexbox的单列布局。PC端采用双栏布局，左侧为会话列表，右侧为聊天窗口。", "breakpoints": "移动端优先。断点设置为：375px (小屏手机), 768px (平板/PC窄视图), 1024px (标准桌面)。", "containerSizes": "在移动端，容器宽度为100%。在桌面端，主要内容区域最大宽度为768px，并水平居中。", "alignment": "整体以左对齐为主，增强内容的可读性。弹窗、标题和需要强调的Slogan采用居中对齐。", "proportions": "弹窗和卡片的宽高比倾向于3:4或4:5，提供舒适的视觉感受。"}, "components": {"buttons": {"primary": "黄色（#FFD60A）背景，文字为深色（#1A1A1A），圆角半径为8px-12px，字体中等粗（500）。", "secondary": "透明或浅灰色（#3A3A3A）背景，白色文字，带有1px的边框（#4F4F4F），圆角与主按钮一致。", "sizes": "提供常规和大型两种尺寸，以适应不同场景。", "states": "悬停（Hover）时，按钮轻微上移2px并增加发光阴影。点击（Active）时，按钮有0.98的缩放效果。禁用（Disabled）时，按钮变为灰色（#4F4F4F）且无响应。"}, "cards": {"style": "采用玻璃拟态风格，具有模糊半透明背景、柔和的内阴影和1px的亮色边框。圆角半径较大，通常为16px-24px。", "spacing": "卡片内部padding为24px，确保内容呼吸感。", "hierarchy": "卡片内部通过排版系统（字号、字重）和间距清晰地组织信息层次。"}, "forms": {"inputs": "输入框背景为深灰色（#1A1A1A），有1px的边框（#4F4F4F）。聚焦时，边框变为品牌紫色（#9B59B6）并带有发光效果。", "labels": "标签位于输入框上方，采用12px/中灰色文字。", "validation": "验证错误时，输入框边框变为红色，并下方出现红色错误提示文字。"}, "navigation": {"style": "移动端采用底部标签栏（Bottom Tab Bar）。背景同样采用玻璃拟态效果，固定在页面底部。", "states": "当前激活的标签图标和文字变为品牌紫色（#9B59B6），并可能有轻微的放大效果。未激活的为中灰色。", "mobile": "底部导航栏是移动端的核心导航方式，包含3-5个核心功能入口。"}}}, "interactions": {"scrollBehavior": {"smoothScrolling": "使用 `scroll-behavior: smooth;`，并可能通过JS库（如Lenis.js）增强平滑滚动的体验。", "parallaxEffects": "背景中的星光点缀元素，在滚动时以不同于内容的速度移动，创造深邃的视差效果。", "scrollTriggers": "聊天记录向上滚动加载历史消息，新消息从底部平滑淡入。", "progressIndicators": "无明显的滚动进度条，强调沉浸式体验。", "stickyElements": "移动端的底部导航栏和PC端的聊天窗口头部是固定元素。"}, "animations": {"fadeInOut": {"trigger": "新消息、新加载的列表项进入视口时触发。", "timing": "动画时长为0.5s-0.8s，营造优雅、不突兀的感觉。", "easing": "使用 `cubic-bezier(0.4, 0, 0.2, 1)` (Ease-in-out)，缓动效果平滑自然。", "stagger": "列表中的多个元素依次出现时，间隔100ms，产生流畅的流动感。", "direction": "元素从下方15px处配合透明度从0到1平滑进入。", "opacity": "从0变化到1。"}, "microInteractions": {"hover": "可交互元素（按钮、链接、列表项）悬停时，有轻微的颜色变化或-2px的上移，并伴随快速（0.2s）的过渡效果。", "click": "点击按钮时有内缩（scale: 0.98）的视觉反馈。点击其他元素可能伴随微妙的涟漪或闪光效果。", "focus": "输入框聚焦时边框变色并发光，提供清晰的视觉焦点。", "loading": "加载状态使用三颗紫色小点依次跳动的动画，或使用旋转的圆环指示器。"}, "pageTransitions": "页面切换采用平滑的淡入淡出效果，时长约300ms。", "modalAnimations": "弹窗从中心点放大（scale从0.95到1）并淡入（opacity从0到1）出现，关闭时反向执行。", "menuAnimations": "底部导航栏切换无复杂动画，以瞬时响应为主，仅颜色和图标状态变化。"}, "performance": {"optimization": "所有动画优先使用CSS `transform` 和 `opacity` 属性，以利用GPU加速，避免重排和重绘。", "hardwareAcceleration": "对动画元素应用 `will-change: transform, opacity;` 或 `transform: translateZ(0);` 来显式创建合成层。", "debounceThrottle": "对窗口resize和滚动事件监听使用节流（throttle）处理。", "lazyLoading": "图片、聊天历史记录等非首屏内容使用懒加载。", "criticalPath": "优先加载和渲染首屏所需的核心CSS和JS。"}, "responsiveness": {"touchInteractions": "按钮和链接的触摸区域（hit area）要大于其视觉区域，方便移动端用户点击。", "gestureSupport": "支持在聊天界面下拉刷新或上拉加载更多。", "deviceAdaptation": "设计在不同尺寸和像素密度的设备上都能保持清晰和一致的视觉体验。"}}, "premiumDesignPrinciples": {"minimalism": {"philosophy": "“少即是多”。设计服务于内容，去除所有不必要的装饰，让用户专注于对话本身。", "whitespace": "将留白作为核心设计元素，用以分割区域、建立节奏和引导视线。", "hierarchy": "通过极简的视觉元素（字重、颜色、间距）构建清晰、直观的信息层级。", "reduction": "每个功能和UI元素都经过审视，只保留最核心的部分。", "focus": "通过高对比度的CTA和聚焦时的动态效果，明确引导用户操作。"}, "professionalism": {"colorRestraint": "严格控制颜色使用，以深色主调和单一功能强调色为主，品牌色仅作点缀，避免色彩混乱。", "typographyConsistency": "全站使用统一的字体家族和排版系统，确保品牌一致性和专业感。", "alignmentPrecision": "所有元素都基于网格和对齐线精确放置，像素级对齐。", "consistencyRules": "所有组件和交互模式在整个应用中保持高度一致。"}, "sophistication": {"subtlety": "动画效果、颜色变化、阴影都非常微妙，追求“润物细无声”的精致感。", "elegance": "流畅的动画曲线、优雅的过渡效果和富有呼吸感的布局共同营造优雅的用户体验。", "timelessness": "选择现代但经典的无衬线字体和极简布局，避免追逐短暂的设计潮流。", "restraint": "克制使用特效和动画，只在必要时用以增强可用性或提供反馈。"}, "modernInteractions": {"smoothness": "所有交互动画的目标是达到60fps，保证丝滑流畅。", "responsiveness": "用户的任何操作都应立即得到视觉反馈，即使数据仍在加载中。", "intuitive": "交互逻辑遵循平台规范和用户心智模型，无需学习成本。", "seamless": "游客登录、注册、开始对话、查看历史等核心流程无缝衔接。", "accessibility": "考虑色盲用户和屏幕阅读器，提供足够的对比度和语义化标签。"}}, "avoidCheapLook": {"colorMistakes": ["避免使用高饱和度的颜色作为大面积背景。", "避免使用超过3种以上的颜色来构建主界面。", "避免使用复杂的、不和谐的彩虹渐变。", "避免文字颜色与背景对比度不足。"], "layoutMistakes": ["避免元素填满整个屏幕，必须保留呼吸空间。", "避免使用混乱、不一致的间距。", "避免僵硬的、完全对称的布局，适当采用不对称设计增加活力。", "避免不遵循网格系统，导致元素错乱。"], "typographyMistakes": ["避免使用非专业、有年代感或过于花哨的字体。", "避免在一个界面中使用超过3种不同的字重。", "避免滥用彩色文字，仅在必要时使用。", "避免过小的字号（小于12px）和过密的行高。"], "interactionMistakes": ["避免缓慢、卡顿、掉帧的动画。", "避免无意义的、干扰视线的炫技动画。", "避免交互反馈不明确或延迟。", "避免全屏突兀的广告和弹窗。"]}, "technicalImplementation": {"cssFeatures": ["backdrop-filter: blur() 实现玻璃拟态效果", "transform: translateZ(0) 或 will-change 启用硬件加速", "cubic-bezier() 自定义缓动函数", "CSS Grid 和 Flexbox 用于现代布局", "CSS 自定义属性 (Variables) 管理设计令牌（颜色、间距等）", "clamp() 函数实现响应式排版", "aspect-ratio 控制媒体元素比例", "scroll-snap-type 改善滚动体验"], "jsPatterns": ["IntersectionObserver 实现滚动触发动画和懒加载", "requestAnimationFrame 保证动画性能", "使用 throttle 对 scroll 和 resize 事件进行性能优化", "Web Components 或基于组件的框架（如Vue, React）进行模块化开发", "WebSockets 或 Server-Sent Events 实现聊天消息的实时流式传输", "前端路由管理单页应用（SPA）的页面状态"], "frameworks": ["CSS框架: Tailwind CSS (用于快速原子化CSS开发) 或 直接使用 PostCSS/Sass", "JavaScript动画库: GSAP (GreenSock Animation Platform) 或 Framer Motion 用于复杂动画", "前端框架: Vue 3 (Composition API) 或 React (Hooks)", "构建工具: Vite"], "bestPractices": ["移动端优先的响应式设计策略。", "渐进增强，保证核心功能在低性能设备上可用。", "使用语义化HTML5标签（<main>, <nav>, <section>）提升可访问性。", "优化图片格式（WebP）和体积，使用CDN加速资源加载。", "代码分割（Code Splitting）按需加载JS模块。", "实施全面的可访问性（a11y）测试。"]}, "designTokens": {"colors": "例如: --color-primary-yellow: #FFD60A; --color-background-dark: #121212;", "spacing": "例如: --space-xs: 4px; --space-sm: 8px; --space-md: 16px;", "typography": "例如: --font-family-base: 'PingFang SC', sans-serif; --font-size-body: 14px;", "shadows": "例如: --shadow-glow-purple: 0 0 12px rgba(155, 89, 182, 0.5);", "borderRadius": "例如: --border-radius-medium: 12px; --border-radius-large: 16px;", "zIndex": "例如: --z-index-modal: 1000; --z-index-navbar: 900;"}}}