const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

/**
 * JWT认证中间件
 */
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.error('缺少认证Token', 40101, 401);
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 查询用户信息
    const users = await query(
      'SELECT id, uuid, email, is_guest, status, role FROM users WHERE uuid = ? AND status = "active"',
      [decoded.uuid]
    );

    if (users.length === 0) {
      return res.error('用户不存在或已被禁用', 40101, 401);
    }

    // 将用户信息添加到请求对象
    req.user = users[0];
    next();
  } catch (error) {
    next(error);
  }
}

/**
 * 可选认证中间件（允许游客和认证用户）
 */
async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const users = await query(
      'SELECT id, uuid, email, is_guest, status, role FROM users WHERE uuid = ? AND status = "active"',
      [decoded.uuid]
    );

    req.user = users.length > 0 ? users[0] : null;
    next();
  } catch (error) {
    // 如果token无效，继续执行但不设置用户信息
    req.user = null;
    next();
  }
}

/**
 * 验证管理员权限中间件
 */
function requireAdmin(req, res, next) {
  // 首先验证JWT token
  authenticateToken(req, res, (err) => {
    if (err) {
      return next(err);
    }

    // 检查用户角色
    if (req.user.role !== 'admin') {
      return res.error('需要管理员权限', 40301, 403);
    }

    next();
  });
}

module.exports = {
  authenticateToken,
  optionalAuth,
  requireAdmin
};
