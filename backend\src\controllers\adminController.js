const adminService = require('../services/adminService');

/**
 * 获取用户列表
 */
async function getUserList(req, res, next) {
  try {
    const { page, limit, search } = req.query;

    const result = await adminService.getUserList(page, limit, search);
    
    res.success(result, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取用户详情
 */
async function getUserDetail(req, res, next) {
  try {
    const { user_uuid } = req.params;

    const user = await adminService.getUserByUuid(user_uuid);
    
    res.success(user, 'Success');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    next(error);
  }
}

/**
 * 更新用户状态
 */
async function updateUserStatus(req, res, next) {
  try {
    const { user_uuid } = req.params;
    const { status } = req.body;

    await adminService.updateUserStatus(user_uuid, status);
    
    res.success(null, 'User status updated successfully');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    if (error.message === 'CANNOT_BAN_ADMIN') {
      return res.error('不能禁用管理员账户', 40001, 400);
    }
    next(error);
  }
}

/**
 * 获取系统统计信息
 */
async function getSystemStats(req, res, next) {
  try {
    const stats = await adminService.getSystemStats();
    
    res.success(stats, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取用户的聊天会话列表
 */
async function getUserChatSessions(req, res, next) {
  try {
    const { user_uuid } = req.params;

    const sessions = await adminService.getUserChatSessions(user_uuid);
    
    res.success(sessions, 'Success');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    next(error);
  }
}

/**
 * 获取用户的订单列表
 */
async function getUserOrders(req, res, next) {
  try {
    const { user_uuid } = req.params;

    const orders = await adminService.getUserOrders(user_uuid);
    
    res.success(orders, 'Success');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    next(error);
  }
}

/**
 * 创建AI角色
 */
async function createAiRole(req, res, next) {
  try {
    const roleData = req.body;

    const newRole = await adminService.createAiRole(roleData);

    res.success(newRole, 'AI role created successfully', 201);
  } catch (error) {
    if (error.message === 'ROLE_NAME_EXISTS') {
      return res.error('角色名称已存在', 40001, 400);
    }
    next(error);
  }
}

/**
 * 更新AI角色
 */
async function updateAiRole(req, res, next) {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const updatedRole = await adminService.updateAiRole(parseInt(id), updateData);

    res.success(updatedRole, 'AI role updated successfully');
  } catch (error) {
    if (error.message === 'ROLE_NOT_FOUND') {
      return res.error('AI角色不存在', 40401, 404);
    }
    if (error.message === 'ROLE_NAME_EXISTS') {
      return res.error('角色名称已存在', 40001, 400);
    }
    if (error.message === 'NO_UPDATE_DATA') {
      return res.error('没有提供更新数据', 40001, 400);
    }
    next(error);
  }
}

/**
 * 删除AI角色
 */
async function deleteAiRole(req, res, next) {
  try {
    const { id } = req.params;

    await adminService.deleteAiRole(parseInt(id));

    res.success(null, 'AI role deleted successfully');
  } catch (error) {
    if (error.message === 'ROLE_NOT_FOUND') {
      return res.error('AI角色不存在', 40401, 404);
    }
    if (error.message === 'ROLE_ALREADY_DELETED') {
      return res.error('AI角色已被删除', 40001, 400);
    }
    if (error.message === 'ROLE_IN_USE') {
      return res.error('AI角色正在使用中，无法删除', 40001, 400);
    }
    next(error);
  }
}

/**
 * 获取订单列表
 */
async function getOrderList(req, res, next) {
  try {
    const { page, limit, status, user_search, order_search } = req.query;

    const filters = {
      status,
      user_search,
      order_search
    };

    const result = await adminService.getOrderList(page, limit, filters);

    res.success(result, 'Success');
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getUserList,
  getUserDetail,
  updateUserStatus,
  getSystemStats,
  getUserChatSessions,
  getUserOrders,
  createAiRole,
  updateAiRole,
  deleteAiRole,
  getOrderList
};
