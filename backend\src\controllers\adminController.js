const adminService = require('../services/adminService');

/**
 * 获取系统统计信息
 */
async function getSystemStats(req, res, next) {
  try {
    const stats = await adminService.getSystemStats();
    
    res.success(stats, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取用户列表
 */
async function getUserList(req, res, next) {
  try {
    const { page, limit, search } = req.query;
    
    const result = await adminService.getUserList(page, limit, search);
    
    res.success(result, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取用户详情
 */
async function getUserDetail(req, res, next) {
  try {
    const { user_uuid } = req.params;
    
    const user = await adminService.getUserByUuid(user_uuid);
    
    if (!user) {
      return res.error('用户不存在', 40401, 404);
    }
    
    res.success(user, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 更新用户状态
 */
async function updateUserStatus(req, res, next) {
  try {
    const { user_uuid } = req.params;
    const { status } = req.body;
    
    await adminService.updateUserStatus(user_uuid, status);
    
    res.success(null, 'User status updated successfully');
  } catch (error) {
    if (error.message === 'USER_NOT_FOUND') {
      return res.error('用户不存在', 40401, 404);
    }
    if (error.message === 'CANNOT_BAN_ADMIN') {
      return res.error('不能禁用管理员账户', 40001, 400);
    }
    next(error);
  }
}

/**
 * 获取用户的聊天会话
 */
async function getUserSessions(req, res, next) {
  try {
    const { user_uuid } = req.params;
    
    // 先检查用户是否存在
    const user = await adminService.getUserByUuid(user_uuid);
    if (!user) {
      return res.error('用户不存在', 40401, 404);
    }
    
    const sessions = await adminService.getUserSessions(user_uuid);
    
    res.success(sessions, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取用户的订单记录
 */
async function getUserOrders(req, res, next) {
  try {
    const { user_uuid } = req.params;
    
    // 先检查用户是否存在
    const user = await adminService.getUserByUuid(user_uuid);
    if (!user) {
      return res.error('用户不存在', 40401, 404);
    }
    
    const orders = await adminService.getUserOrders(user_uuid);
    
    res.success(orders, 'Success');
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getSystemStats,
  getUserList,
  getUserDetail,
  updateUserStatus,
  getUserSessions,
  getUserOrders
};
