const express = require('express');
const aiRoleController = require('../controllers/aiRoleController');
const { validateParams, aiRoleIdSchema } = require('../validators/aiRole');

const router = express.Router();

/**
 * @route GET /api/v1/ai-roles
 * @desc 获取所有启用的AI角色列表
 * @access Public
 */
router.get('/', aiRoleController.getAiRoles);

/**
 * @route GET /api/v1/ai-roles/:id
 * @desc 获取指定AI角色的详细信息
 * @access Public
 */
router.get('/:id', validateParams(aiRoleIdSchema), aiRoleController.getAiRoleById);

module.exports = router;
