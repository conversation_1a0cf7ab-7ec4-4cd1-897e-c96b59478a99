{"name": "ai-companion-backend", "version": "1.0.0", "description": "AI伙伴聊天应用后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["ai", "chat", "companion", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.0", "uuid": "^9.0.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}