const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');

describe('会员功能测试', () => {
  let userToken;
  let userId;
  let planId;
  const testEmail = `membership_test_${Date.now()}@example.com`;
  const testPassword = 'TestPassword123';

  beforeAll(async () => {
    // 注册测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (registerResponse.body.code === 201) {
      userToken = registerResponse.body.data.token;
      
      // 获取用户ID
      const users = await query('SELECT id FROM users WHERE email = ?', [testEmail]);
      userId = users[0].id;
    }

    // 获取一个订阅套餐
    const plansResponse = await request(app)
      .get('/api/v1/plans');
    
    if (plansResponse.body.data && plansResponse.body.data.length > 0) {
      planId = plansResponse.body.data.find(plan => plan.plan_type === 'subscription')?.id;
    }
  });

  describe('用户个人中心会员状态', () => {
    test('普通用户个人中心应该显示非会员状态', async () => {
      if (!userToken) {
        console.log('跳过测试：用户注册失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/me/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('membership');
      expect(response.body.data.membership.is_member).toBe(false);
      expect(response.body.data.membership.subscription_type).toBeNull();
      expect(response.body.data.membership.expires_at).toBeNull();
      expect(response.body.data.membership.plan_name).toBeNull();
      expect(response.body.data.membership.remaining_days).toBe(0);
    });

    test('购买订阅套餐后应该显示会员状态', async () => {
      if (!userToken || !planId || !userId) {
        console.log('跳过测试：前置条件不满足');
        return;
      }

      // 模拟购买订阅套餐 - 先创建订单，再创建订阅记录
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30); // 30天后过期

      // 创建测试订单
      const orderResult = await query(
        `INSERT INTO orders (user_id, plan_id, order_number, amount, status)
         VALUES (?, ?, ?, 29.99, 'completed')`,
        [userId, planId, `TEST_ORDER_${Date.now()}`]
      );
      const orderId = orderResult.insertId;

      // 创建订阅记录
      await query(
        `INSERT INTO user_subscriptions (user_id, plan_id, order_id, status, start_date, end_date)
         VALUES (?, ?, ?, 'active', CURRENT_TIMESTAMP, ?)`,
        [userId, planId, orderId, expiresAt]
      );

      // 再次获取个人信息
      const response = await request(app)
        .get('/api/v1/me/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('membership');
      expect(response.body.data.membership.is_member).toBe(true);
      expect(response.body.data.membership.subscription_type).toBe('subscription');
      expect(response.body.data.membership.expires_at).toBeTruthy();
      expect(response.body.data.membership.plan_name).toBeTruthy();
      expect(response.body.data.membership.remaining_days).toBeGreaterThan(25); // 应该接近30天
    });
  });

  describe('会员聊天权益', () => {
    test('会员用户应该享受更高的每日消息额度', async () => {
      if (!userToken) {
        console.log('跳过测试：用户注册失败');
        return;
      }

      // 获取AI角色
      const rolesResponse = await request(app)
        .get('/api/v1/ai-roles');

      if (rolesResponse.body.data && rolesResponse.body.data.length > 0) {
        const validRoleId = rolesResponse.body.data[0].id;

        // 创建会话
        const sessionResponse = await request(app)
          .post('/api/v1/chat/sessions')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            ai_role_id: validRoleId
          });

        if (sessionResponse.body.data) {
          const sessionUuid = sessionResponse.body.data.uuid;

          // 尝试发送消息（会员应该有更高额度）
          const messageResponse = await request(app)
            .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
            .set('Authorization', `Bearer ${userToken}`)
            .send({
              content: '测试会员聊天权益'
            });

          // 会员用户应该能够成功发送消息
          expect(messageResponse.status).toBeLessThan(400);
          
          if (messageResponse.body.code === 200) {
            expect(messageResponse.body.data).toHaveProperty('content');
          }
        }
      }
    });
  });

  describe('会员状态查询', () => {
    test('应该能够正确识别会员状态', async () => {
      if (!userId) {
        console.log('跳过测试：用户ID不存在');
        return;
      }

      const { getUserMembershipInfo } = require('../src/services/userCenterService');
      
      const membershipInfo = await getUserMembershipInfo(userId);
      
      expect(membershipInfo).toHaveProperty('is_member');
      expect(membershipInfo).toHaveProperty('subscription_type');
      expect(membershipInfo).toHaveProperty('expires_at');
      expect(membershipInfo).toHaveProperty('plan_name');
      expect(membershipInfo).toHaveProperty('remaining_days');
      
      // 如果之前创建了订阅，应该是会员
      if (membershipInfo.is_member) {
        expect(membershipInfo.subscription_type).toBe('subscription');
        expect(membershipInfo.remaining_days).toBeGreaterThan(0);
      }
    });
  });

  describe('会员过期处理', () => {
    test('过期的订阅应该不再显示为会员', async () => {
      if (!userId) {
        console.log('跳过测试：用户ID不存在');
        return;
      }

      // 创建一个已过期的订阅
      const expiredDate = new Date();
      expiredDate.setDate(expiredDate.getDate() - 1); // 昨天过期

      // 创建测试订单
      const expiredOrderResult = await query(
        `INSERT INTO orders (user_id, plan_id, order_number, amount, status)
         VALUES (?, ?, ?, 29.99, 'completed')`,
        [userId, planId || 1, `EXPIRED_ORDER_${Date.now()}`]
      );
      const expiredOrderId = expiredOrderResult.insertId;

      await query(
        `INSERT INTO user_subscriptions (user_id, plan_id, order_id, status, start_date, end_date)
         VALUES (?, ?, ?, 'expired', CURRENT_TIMESTAMP, ?)`,
        [userId, planId || 1, expiredOrderId, expiredDate]
      );

      const { getUserMembershipInfo } = require('../src/services/userCenterService');
      const membershipInfo = await getUserMembershipInfo(userId);
      
      // 应该检查最新的活跃订阅，过期的不应该影响结果
      expect(membershipInfo).toHaveProperty('is_member');
    });
  });

  afterAll(async () => {
    // 清理测试数据
    if (userId) {
      await query('DELETE FROM user_subscriptions WHERE user_id = ?', [userId]);
      await query('DELETE FROM orders WHERE user_id = ?', [userId]);
      await query('DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id = ?)', [userId]);
      await query('DELETE FROM chat_sessions WHERE user_id = ?', [userId]);
      await query('DELETE FROM users WHERE id = ?', [userId]);
    }
  });
});
