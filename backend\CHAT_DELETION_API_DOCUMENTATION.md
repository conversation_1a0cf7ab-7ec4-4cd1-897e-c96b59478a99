# AI伙伴聊天应用 - 聊天删除功能API文档

## 概述

本文档描述了AI伙伴聊天应用**聊天删除功能**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1/chat`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Chat session deleted successfully",
  "data": null
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "聊天会话不存在或无权访问",
  "data": null
}
```

---

## 删除功能接口

### 1. 删除聊天会话

**接口描述**: 删除指定的聊天会话及其所有消息记录

- **URL**: `DELETE /sessions/{session_uuid}`
- **认证**: 需要JWT Token

**路径参数**:
- `session_uuid`: 会话的UUID（必须是有效的UUID格式）

**响应示例**:
```json
{
  "code": 200,
  "message": "Chat session deleted successfully",
  "data": null
}
```

**业务规则**:
- 只能删除属于当前用户的会话
- 删除会话时会同时删除该会话的所有消息记录
- 删除操作不可逆

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问
- `400 Bad Request`: UUID格式无效
- `401 Unauthorized`: 未认证

### 2. 清空会话消息历史

**接口描述**: 清空指定会话的所有消息记录，但保留会话本身

- **URL**: `DELETE /sessions/{session_uuid}/messages`
- **认证**: 需要JWT Token

**路径参数**:
- `session_uuid`: 会话的UUID（必须是有效的UUID格式）

**响应示例**:
```json
{
  "code": 200,
  "message": "Session messages cleared successfully",
  "data": null
}
```

**业务规则**:
- 只能清空属于当前用户的会话消息
- 清空后会话标题重置为"新的对话"
- 会话本身保留，可以继续使用
- 清空操作不可逆

**错误响应**:
- `404 Not Found`: 会话不存在或无权访问
- `400 Bad Request`: UUID格式无效
- `401 Unauthorized`: 未认证

### 3. 删除所有聊天记录

**接口描述**: 删除当前用户的所有聊天会话和消息记录

- **URL**: `DELETE /sessions`
- **认证**: 需要JWT Token

**响应示例**:
```json
{
  "code": 200,
  "message": "All chat records deleted successfully",
  "data": null
}
```

**业务规则**:
- 删除当前用户的所有聊天会话
- 同时删除所有相关的消息记录
- 删除操作不可逆
- 删除后用户可以重新创建新的会话

**错误响应**:
- `401 Unauthorized`: 未认证

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 删除指定会话
async function deleteSession(token, sessionUuid) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('会话删除成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('删除会话失败:', error);
    throw error;
  }
}

// 2. 清空会话消息
async function clearSessionMessages(token, sessionUuid) {
  try {
    const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('消息历史清空成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('清空消息失败:', error);
    throw error;
  }
}

// 3. 删除所有聊天记录
async function deleteAllChats(token) {
  try {
    const response = await fetch('/api/v1/chat/sessions', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('所有聊天记录删除成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('删除所有聊天失败:', error);
    throw error;
  }
}

// 使用示例
async function example() {
  const token = 'your-jwt-token';
  const sessionUuid = 'session-uuid-here';

  // 确认删除
  if (confirm('确定要删除这个会话吗？删除后无法恢复。')) {
    await deleteSession(token, sessionUuid);
  }

  // 确认清空
  if (confirm('确定要清空消息历史吗？清空后无法恢复。')) {
    await clearSessionMessages(token, sessionUuid);
  }

  // 确认删除全部
  if (confirm('确定要删除所有聊天记录吗？删除后无法恢复。')) {
    await deleteAllChats(token);
  }
}
```

### React Hook 示例

```javascript
import { useState, useCallback } from 'react';

export function useChatDeletion(token, onSuccess) {
  const [loading, setLoading] = useState(false);

  const deleteSession = useCallback(async (sessionUuid) => {
    if (!confirm('确定要删除这个会话吗？删除后无法恢复。')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const result = await response.json();
      if (result.code === 200) {
        onSuccess?.('会话删除成功');
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [token, onSuccess]);

  const clearMessages = useCallback(async (sessionUuid) => {
    if (!confirm('确定要清空消息历史吗？清空后无法恢复。')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/chat/sessions/${sessionUuid}/messages`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const result = await response.json();
      if (result.code === 200) {
        onSuccess?.('消息历史清空成功');
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('清空失败:', error);
      alert('清空失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [token, onSuccess]);

  const deleteAllChats = useCallback(async () => {
    if (!confirm('确定要删除所有聊天记录吗？删除后无法恢复。')) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/chat/sessions', {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const result = await response.json();
      if (result.code === 200) {
        onSuccess?.('所有聊天记录删除成功');
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  }, [token, onSuccess]);

  return {
    deleteSession,
    clearMessages,
    deleteAllChats,
    loading
  };
}
```

---

## 安全考虑

1. **权限验证**: 所有接口都需要JWT认证
2. **所有权检查**: 只能操作属于当前用户的会话
3. **参数验证**: 严格的UUID格式验证
4. **操作确认**: 建议在前端添加确认对话框
5. **不可逆操作**: 删除操作无法撤销，需谨慎使用

---

## 注意事项

1. **数据安全**: 删除操作不可逆，请确保用户充分理解
2. **级联删除**: 删除会话会同时删除所有相关消息
3. **会话状态**: 删除当前活跃会话后需要更新前端状态
4. **用户体验**: 建议提供明确的确认提示和操作反馈
5. **错误处理**: 妥善处理网络错误和权限错误

---

## 测试覆盖

聊天删除功能包含完整的测试覆盖：

### 删除功能测试
- ✅ 删除聊天会话测试（成功删除、权限验证）
- ✅ 清空会话消息测试（保留会话、清空消息）
- ✅ 删除所有聊天记录测试（批量删除）
- ✅ 参数验证测试（UUID格式验证）
- ✅ 权限验证测试（未认证用户拒绝）
- ✅ 错误处理测试（不存在的会话等）

所有7个测试用例均已通过验证，删除功能完整可用。
