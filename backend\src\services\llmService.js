const axios = require('axios');

/**
 * LLM服务配置
 */
const LLM_CONFIG = {
  apiKey: process.env.LLM_API_KEY,
  baseURL: process.env.LLM_BASE_URL || 'https://www.sophnet.com/api/open-apis/v1',
  model: process.env.LLM_MODEL || 'DeepSeek-V3-Fast',
  maxTokens: parseInt(process.env.LLM_MAX_TOKENS) || 2000,
  temperature: parseFloat(process.env.LLM_TEMPERATURE) || 0.7
};

/**
 * 调用LLM API获取回复
 * @param {Array} messages - 消息历史数组
 * @param {string} systemPrompt - 系统提示词
 * @returns {Promise<string>} AI回复内容
 */
async function getChatCompletion(messages, systemPrompt) {
  try {
    // 构建完整的消息数组
    const fullMessages = [
      {
        role: 'system',
        content: systemPrompt
      },
      ...messages
    ];

    const requestData = {
      model: LLM_CONFIG.model,
      messages: fullMessages,
      max_tokens: LLM_CONFIG.maxTokens,
      temperature: LLM_CONFIG.temperature,
      stream: false
    };

    const response = await axios.post(
      `${LLM_CONFIG.baseURL}/chat/completions`,
      requestData,
      {
        headers: {
          'Authorization': `Bearer ${LLM_CONFIG.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30秒超时
      }
    );

    // 检查响应格式
    if (!response.data || !response.data.choices || response.data.choices.length === 0) {
      throw new Error('Invalid response format from LLM API');
    }

    const aiMessage = response.data.choices[0].message;
    if (!aiMessage || !aiMessage.content) {
      throw new Error('No content in LLM response');
    }

    return aiMessage.content.trim();

  } catch (error) {
    console.error('LLM API调用失败:', error.message);
    
    // 处理不同类型的错误
    if (error.response) {
      // API返回了错误响应
      const status = error.response.status;
      const message = error.response.data?.message || error.response.statusText;
      
      if (status === 401) {
        throw new Error('LLM API认证失败，请检查API密钥');
      } else if (status === 429) {
        throw new Error('LLM API调用频率超限，请稍后重试');
      } else if (status >= 500) {
        throw new Error('LLM服务暂时不可用，请稍后重试');
      } else {
        throw new Error(`LLM API错误: ${message}`);
      }
    } else if (error.code === 'ECONNABORTED') {
      // 请求超时
      throw new Error('LLM API请求超时，请稍后重试');
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      // 网络连接问题
      throw new Error('无法连接到LLM服务，请检查网络连接');
    } else {
      // 其他错误
      throw new Error('LLM服务调用失败，请稍后重试');
    }
  }
}

/**
 * 格式化消息历史为LLM API格式
 * @param {Array} chatMessages - 数据库中的消息记录
 * @returns {Array} 格式化后的消息数组
 */
function formatMessagesForLLM(chatMessages) {
  return chatMessages.map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

/**
 * 获取上下文消息（最近N条）
 * @param {Array} allMessages - 所有消息
 * @param {number} limit - 限制条数，默认10条
 * @returns {Array} 上下文消息
 */
function getContextMessages(allMessages, limit = 10) {
  // 按时间排序，取最近的N条消息
  const sortedMessages = allMessages.sort((a, b) => 
    new Date(a.created_at) - new Date(b.created_at)
  );
  
  // 如果消息数量超过限制，只取最近的消息
  if (sortedMessages.length > limit) {
    return sortedMessages.slice(-limit);
  }
  
  return sortedMessages;
}

module.exports = {
  getChatCompletion,
  formatMessagesForLLM,
  getContextMessages
};
