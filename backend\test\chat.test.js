const request = require('supertest');
const app = require('../src/app');

describe('聊天模块测试', () => {
  let userToken;
  let guestToken;
  let sessionUuid;
  let validRoleId;

  beforeAll(async () => {
    // 创建测试用户并获取token
    const testEmail = `chattest${Date.now()}@example.com`;
    const testPassword = 'test123456';

    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;

    // 创建游客用户
    const guestResponse = await request(app)
      .post('/api/v1/auth/guest');

    guestToken = guestResponse.body.data.token;

    // 获取一个有效的AI角色ID
    const rolesResponse = await request(app)
      .get('/api/v1/ai-roles');

    if (rolesResponse.body.data.length > 0) {
      validRoleId = rolesResponse.body.data[0].id;
    }
  });

  describe('POST /api/v1/chat/sessions', () => {
    test('应该成功创建聊天会话', async () => {
      if (!validRoleId) {
        console.log('跳过测试：没有可用的AI角色');
        return;
      }

      const response = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: validRoleId
        })
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.message).toBe('Chat session created successfully');
      expect(response.body.data).toHaveProperty('uuid');
      expect(response.body.data).toHaveProperty('ai_role_id', validRoleId);
      expect(response.body.data).toHaveProperty('title');
      expect(response.body.data).toHaveProperty('created_at');

      sessionUuid = response.body.data.uuid;
    });

    test('游客用户也应该能创建聊天会话', async () => {
      if (!validRoleId) {
        console.log('跳过测试：没有可用的AI角色');
        return;
      }

      const response = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${guestToken}`)
        .send({
          ai_role_id: validRoleId
        })
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.data).toHaveProperty('uuid');
    });

    test('应该拒绝不存在的AI角色ID', async () => {
      const response = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: 99999
        })
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('AI角色不存在或已禁用');
    });

    test('应该拒绝无效的AI角色ID格式', async () => {
      const response = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: 'invalid'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .post('/api/v1/chat/sessions')
        .send({
          ai_role_id: validRoleId
        })
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('GET /api/v1/chat/sessions', () => {
    test('应该成功获取用户的聊天会话列表', async () => {
      const response = await request(app)
        .get('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Sessions retrieved successfully');
      expect(Array.isArray(response.body.data)).toBe(true);

      if (response.body.data.length > 0) {
        const session = response.body.data[0];
        expect(session).toHaveProperty('uuid');
        expect(session).toHaveProperty('title');
        expect(session).toHaveProperty('ai_role_id');
        expect(session).toHaveProperty('ai_role_name');
        expect(session).toHaveProperty('updated_at');
      }
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .get('/api/v1/chat/sessions')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('POST /api/v1/chat/sessions/:session_uuid/messages', () => {
    test('应该成功发送消息（模拟，不调用真实LLM）', async () => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        return;
      }

      // 注意：这个测试可能会调用真实的LLM API，在测试环境中可能会失败
      // 这里我们主要测试参数验证和基本流程
      const response = await request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '你好，这是一条测试消息'
        });

      // 可能成功（200）或LLM调用失败（500）
      expect([200, 500]).toContain(response.status);

      if (response.status === 200) {
        expect(response.body.code).toBe(200);
        expect(response.body.message).toBe('Message sent successfully');
        expect(response.body.data).toHaveProperty('role', 'assistant');
        expect(response.body.data).toHaveProperty('content');
        expect(response.body.data).toHaveProperty('created_at');
      } else {
        expect(response.body.code).toBe(50002);
      }
    });

    test('应该拒绝空消息', async () => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        return;
      }

      const response = await request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: ''
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝过长的消息', async () => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        return;
      }

      const longMessage = 'a'.repeat(2001);
      const response = await request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: longMessage
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝无效的会话UUID', async () => {
      const response = await request(app)
        .post('/api/v1/chat/sessions/invalid-uuid/messages')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '测试消息'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝不存在的会话', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      const response = await request(app)
        .post(`/api/v1/chat/sessions/${fakeUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '测试消息'
        })
        .expect(404);

      expect(response.body.code).toBe(40401);
    });
  });

  describe('GET /api/v1/chat/sessions/:session_uuid/messages', () => {
    test('应该成功获取会话历史消息', async () => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/chat/sessions/${sessionUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Messages retrieved successfully');
      expect(Array.isArray(response.body.data)).toBe(true);

      // 如果有消息，检查消息格式
      if (response.body.data.length > 0) {
        const message = response.body.data[0];
        expect(message).toHaveProperty('role');
        expect(message).toHaveProperty('content');
        expect(message).toHaveProperty('created_at');
        expect(['user', 'assistant']).toContain(message.role);
      }
    });

    test('应该拒绝无效的会话UUID', async () => {
      const response = await request(app)
        .get('/api/v1/chat/sessions/invalid-uuid/messages')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝不存在的会话', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      const response = await request(app)
        .get(`/api/v1/chat/sessions/${fakeUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
    });
  });
});
