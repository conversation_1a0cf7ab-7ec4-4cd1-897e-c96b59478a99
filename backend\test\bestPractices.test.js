const request = require('supertest');
const app = require('../src/app');
const { batchResetDailyQuota } = require('../src/utils/dailyQuota');
const { sanitizeText, validateEmail, validatePassword } = require('../src/utils/security');
const { createPagination } = require('../src/utils/pagination');
const { queue } = require('../src/utils/messageQueue');

describe('API最佳实践测试', () => {
  let userToken;
  let guestToken;
  const testEmail = `best_practices_${Date.now()}@example.com`;
  const testPassword = 'TestPassword123';

  beforeAll(async () => {
    // 创建游客用户
    const guestResponse = await request(app)
      .post('/api/v1/auth/guest');

    if (guestResponse.body.code === 201) {
      guestToken = guestResponse.body.data.token;
    }
  });

  describe('1. 游客转正功能', () => {
    test('游客用户应该能够转正为注册用户并保留数据', async () => {
      if (!guestToken) {
        console.log('跳过测试：游客创建失败');
        return;
      }

      // 游客转正注册
      const response = await request(app)
        .post('/api/v1/auth/register')
        .set('x-guest-token', guestToken)
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(201);

      expect(response.body.code).toBe(201);
      expect(response.body.message).toContain('converted');
      expect(response.body.data.converted).toBe(true);
      expect(response.body.data.user.email).toBe(testEmail);
      expect(response.body.data.user.is_guest).toBe(false);

      userToken = response.body.data.token;
    });

    test('转正后的用户应该能够正常登录', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data.user.email).toBe(testEmail);
      expect(response.body.data.user.is_guest).toBe(false);
    });

    test('应该拒绝重复的邮箱注册', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(409);

      expect(response.body.code).toBe(40901);
      expect(response.body.message).toBe('邮箱已存在');
    });
  });

  describe('2. 每日额度重置功能', () => {
    test('每日额度重置脚本应该正常工作', async () => {
      const resetCount = await batchResetDailyQuota();
      expect(typeof resetCount).toBe('number');
      expect(resetCount).toBeGreaterThanOrEqual(0);
    });

    test('用户发送消息应该检查额度限制', async () => {
      if (!userToken) {
        console.log('跳过测试：用户未注册');
        return;
      }

      // 获取AI角色
      const rolesResponse = await request(app)
        .get('/api/v1/ai-roles');

      const validRoleId = rolesResponse.body.data[0].id;

      // 创建会话
      const sessionResponse = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: validRoleId
        });

      const sessionUuid = sessionResponse.body.data.uuid;

      // 发送消息（应该成功，因为有每日免费额度）
      const messageResponse = await request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '测试每日额度功能'
        });

      // 根据用户类型，可能成功或失败
      if (messageResponse.status === 200) {
        expect(messageResponse.body.code).toBe(200);
      } else if (messageResponse.status === 403) {
        expect(messageResponse.body.code).toBe(40301);
        expect(messageResponse.body.message).toContain('额度');
      }
    });
  });

  describe('3. 安全性验证', () => {
    test('输入清理功能应该正常工作', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const cleaned = sanitizeText(maliciousInput, { allowSpecialChars: false });

      expect(cleaned).not.toContain('<script>');
      expect(cleaned).not.toContain('</script>');
      expect(cleaned).toContain('Hello World');
    });

    test('邮箱验证应该正确工作', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null)).toBe(false);
    });

    test('密码强度验证应该正确工作', () => {
      const strongPassword = validatePassword('StrongPass123');
      expect(strongPassword.valid).toBe(true);
      expect(strongPassword.errors).toHaveLength(0);

      const weakPassword = validatePassword('123');
      expect(weakPassword.valid).toBe(false);
      expect(weakPassword.errors.length).toBeGreaterThan(0);
    });

    test('应该拒绝过大的请求', async () => {
      const largeContent = 'A'.repeat(10000); // 10KB内容

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: '<EMAIL>',
          password: largeContent
        });

      // 应该被输入清理或验证拒绝
      expect(response.status).toBeGreaterThanOrEqual(400);
    });
  });

  describe('4. 分页功能', () => {
    test('分页工具应该正确计算分页参数', () => {
      const pagination = createPagination({ page: 2, limit: 10 });
      
      expect(pagination.page).toBe(2);
      expect(pagination.limit).toBe(10);
      expect(pagination.offset).toBe(10);
    });

    test('分页应该有合理的限制', () => {
      const pagination = createPagination({ page: 1, limit: 1000 }, { maxLimit: 100 });
      
      expect(pagination.limit).toBe(100); // 应该被限制到最大值
    });

    test('分页元数据应该正确计算', () => {
      const pagination = createPagination({ page: 2, limit: 10 });
      const metadata = pagination.getMetadata(25);
      
      expect(metadata.totalPages).toBe(3);
      expect(metadata.hasNext).toBe(true);
      expect(metadata.hasPrev).toBe(true);
      expect(metadata.nextPage).toBe(3);
      expect(metadata.prevPage).toBe(1);
    });

    test('获取会话列表应该支持分页', async () => {
      if (!userToken) {
        console.log('跳过测试：用户未注册');
        return;
      }

      const response = await request(app)
        .get('/api/v1/chat/sessions?page=1&limit=5')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // 如果有分页信息，验证格式
      if (response.body.pagination) {
        expect(response.body.pagination).toHaveProperty('page');
        expect(response.body.pagination).toHaveProperty('limit');
        expect(response.body.pagination).toHaveProperty('total');
      }
    });
  });

  describe('5. 异步处理', () => {
    test('消息队列应该能够处理任务', (done) => {
      const testData = { message: 'test queue task' };
      
      // 注册临时处理器
      queue.process('test-queue', async (data) => {
        expect(data.message).toBe('test queue task');
        done();
        return 'success';
      }, { concurrency: 1 });

      // 添加任务
      queue.enqueue('test-queue', testData);
    });

    test('消息队列应该支持重试机制', (done) => {
      let attempts = 0;
      
      queue.process('retry-test-queue', async (data) => {
        attempts++;
        if (attempts < 2) {
          throw new Error('Simulated failure');
        }
        expect(attempts).toBe(2);
        done();
        return 'success after retry';
      }, { concurrency: 1 });

      queue.enqueue('retry-test-queue', { test: 'retry' }, { maxRetries: 3 });
    });

    test('应该能够获取队列状态', () => {
      const status = queue.getQueueStatus('test-queue');
      
      expect(status).toHaveProperty('name');
      expect(status).toHaveProperty('waiting');
      expect(status).toHaveProperty('processing');
      expect(status).toHaveProperty('total');
    });
  });

  describe('6. 频率限制', () => {
    test('应该限制过于频繁的请求', async () => {
      const promises = [];

      // 快速发送多个请求
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/v1/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(promises);

      // 检查是否有频率限制响应，如果没有也不算失败（可能频率限制配置较宽松）
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      const failedResponses = responses.filter(res => res.status >= 400);

      // 至少应该有一些失败的响应（401或429）
      expect(failedResponses.length).toBeGreaterThan(0);
    });
  });

  afterAll(async () => {
    // 清理测试队列
    queue.clearQueue('test-queue');
    queue.clearQueue('retry-test-queue');
  });
});
