const { v4: uuidv4 } = require('uuid');
const { query } = require('../config/database');
const { getChatCompletion, getChatCompletionStream, formatMessagesForLLM, getContextMessages } = require('./llmService');

/**
 * 创建新的聊天会话
 */
async function createChatSession(userId, aiRoleId) {
  // 检查AI角色是否存在且启用
  const aiRoles = await query(
    'SELECT id, name FROM ai_roles WHERE id = ? AND is_active = ?',
    [aiRoleId, true]
  );

  if (aiRoles.length === 0) {
    throw new Error('AI_ROLE_NOT_FOUND');
  }

  const sessionUuid = uuidv4();
  const defaultTitle = '新的对话';

  // 创建会话记录
  await query(
    'INSERT INTO chat_sessions (uuid, user_id, ai_role_id, title) VALUES (?, ?, ?, ?)',
    [sessionUuid, userId, aiRoleId, defaultTitle]
  );

  // 获取创建的会话信息
  const sessions = await query(
    'SELECT uuid, ai_role_id, title, created_at FROM chat_sessions WHERE uuid = ?',
    [sessionUuid]
  );

  return sessions[0];
}

/**
 * 发送消息并获取AI回复
 */
async function sendMessage(sessionUuid, userId, content) {
  // 验证会话是否存在且属于当前用户
  const sessions = await query(
    'SELECT cs.id, cs.ai_role_id, ar.system_prompt FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.uuid = ? AND cs.user_id = ? AND ar.is_active = ?',
    [sessionUuid, userId, true]
  );

  if (sessions.length === 0) {
    throw new Error('SESSION_NOT_FOUND');
  }

  const session = sessions[0];
  const sessionId = session.id;
  const systemPrompt = session.system_prompt;

  // 保存用户消息
  await query(
    'INSERT INTO chat_messages (session_id, role, content) VALUES (?, ?, ?)',
    [sessionId, 'user', content]
  );

  try {
    // 获取历史消息作为上下文
    const historyMessages = await query(
      'SELECT role, content, created_at FROM chat_messages WHERE session_id = ? ORDER BY created_at ASC',
      [sessionId]
    );

    // 获取上下文消息（包括刚刚保存的用户消息）
    const contextMessages = getContextMessages(historyMessages, 10);
    const formattedMessages = formatMessagesForLLM(contextMessages);

    // 调用LLM获取回复
    const aiReply = await getChatCompletion(formattedMessages, systemPrompt);

    // 保存AI回复
    await query(
      'INSERT INTO chat_messages (session_id, role, content) VALUES (?, ?, ?)',
      [sessionId, 'assistant', aiReply]
    );

    // 更新会话的最后更新时间
    await query(
      'UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [sessionId]
    );

    // 返回AI回复信息
    const aiMessages = await query(
      'SELECT role, content, emotion, created_at FROM chat_messages WHERE session_id = ? AND role = "assistant" ORDER BY created_at DESC LIMIT 1',
      [sessionId]
    );

    return aiMessages[0];

  } catch (error) {
    console.error('AI回复生成失败:', error.message);
    
    // 如果AI调用失败，保存一个错误回复
    const errorReply = '抱歉，我现在无法回复您的消息，请稍后再试。';
    await query(
      'INSERT INTO chat_messages (session_id, role, content) VALUES (?, ?, ?)',
      [sessionId, 'assistant', errorReply]
    );

    // 抛出具体的错误信息
    throw new Error(`AI_REPLY_FAILED: ${error.message}`);
  }
}

/**
 * 获取会话历史消息
 */
async function getSessionMessages(sessionUuid, userId) {
  // 验证会话是否存在且属于当前用户
  const sessions = await query(
    'SELECT id FROM chat_sessions WHERE uuid = ? AND user_id = ?',
    [sessionUuid, userId]
  );

  if (sessions.length === 0) {
    throw new Error('SESSION_NOT_FOUND');
  }

  const sessionId = sessions[0].id;

  // 获取所有消息
  const messages = await query(
    'SELECT role, content, emotion, created_at FROM chat_messages WHERE session_id = ? ORDER BY created_at ASC',
    [sessionId]
  );

  return messages;
}

/**
 * 获取用户的所有聊天会话
 */
async function getUserSessions(userId) {
  const sessions = await query(
    'SELECT cs.uuid, cs.title, cs.ai_role_id, ar.name as ai_role_name, cs.updated_at ' +
    'FROM chat_sessions cs ' +
    'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
    'WHERE cs.user_id = ? ' +
    'ORDER BY cs.updated_at DESC',
    [userId]
  );

  return sessions;
}

/**
 * 检查会话是否属于用户
 */
async function checkSessionOwnership(sessionUuid, userId) {
  const sessions = await query(
    'SELECT id FROM chat_sessions WHERE uuid = ? AND user_id = ?',
    [sessionUuid, userId]
  );

  return sessions.length > 0;
}

/**
 * 发送消息并获取AI流式回复
 */
async function sendMessageStream(sessionUuid, userId, content, onChunk, onComplete, onError) {
  try {
    // 验证会话是否存在且属于当前用户
    const sessions = await query(
      'SELECT cs.id, cs.ai_role_id, ar.system_prompt FROM chat_sessions cs ' +
      'JOIN ai_roles ar ON cs.ai_role_id = ar.id ' +
      'WHERE cs.uuid = ? AND cs.user_id = ? AND ar.is_active = ?',
      [sessionUuid, userId, true]
    );

    if (sessions.length === 0) {
      throw new Error('SESSION_NOT_FOUND');
    }

    const session = sessions[0];
    const sessionId = session.id;
    const systemPrompt = session.system_prompt;

    // 保存用户消息
    await query(
      'INSERT INTO chat_messages (session_id, role, content) VALUES (?, ?, ?)',
      [sessionId, 'user', content]
    );

    // 获取历史消息作为上下文
    const historyMessages = await query(
      'SELECT role, content, created_at FROM chat_messages WHERE session_id = ? ORDER BY created_at ASC',
      [sessionId]
    );

    // 获取上下文消息（包括刚刚保存的用户消息）
    const contextMessages = getContextMessages(historyMessages, 10);
    const formattedMessages = formatMessagesForLLM(contextMessages);

    // 调用LLM获取流式回复
    await getChatCompletionStream(
      formattedMessages,
      systemPrompt,
      // onChunk回调：每收到一个数据块时调用
      (chunk, fullContent) => {
        onChunk(chunk, fullContent);
      },
      // onComplete回调：完成时调用
      async (fullContent) => {
        try {
          // 保存完整的AI回复
          await query(
            'INSERT INTO chat_messages (session_id, role, content) VALUES (?, ?, ?)',
            [sessionId, 'assistant', fullContent]
          );

          // 更新会话的最后更新时间
          await query(
            'UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [sessionId]
          );

          // 获取保存的AI消息信息
          const aiMessages = await query(
            'SELECT role, content, emotion, created_at FROM chat_messages WHERE session_id = ? AND role = "assistant" ORDER BY created_at DESC LIMIT 1',
            [sessionId]
          );

          onComplete(aiMessages[0]);
        } catch (saveError) {
          console.error('保存AI回复失败:', saveError.message);
          onError(new Error('保存AI回复失败'));
        }
      },
      // onError回调：出错时调用
      async (error) => {
        console.error('AI流式回复生成失败:', error.message);

        // 如果AI调用失败，保存一个错误回复
        const errorReply = '抱歉，我现在无法回复您的消息，请稍后再试。';
        try {
          await query(
            'INSERT INTO chat_messages (session_id, role, content) VALUES (?, ?, ?)',
            [sessionId, 'assistant', errorReply]
          );
        } catch (saveError) {
          console.error('保存错误回复失败:', saveError.message);
        }

        onError(error);
      }
    );

  } catch (error) {
    onError(error);
  }
}

module.exports = {
  createChatSession,
  sendMessage,
  sendMessageStream,
  getSessionMessages,
  getUserSessions,
  checkSessionOwnership
};
