const { v4: uuidv4 } = require('uuid');
const { query } = require('../config/database');
const { hashPassword, verifyPassword } = require('../utils/password');
const { generateToken, verifyToken } = require('../utils/jwt');

/**
 * 创建游客用户
 */
async function createGuestUser() {
  const userUuid = uuidv4();
  
  await query(
    'INSERT INTO users (uuid, is_guest) VALUES (?, ?)',
    [userUuid, true]
  );

  const user = {
    uuid: userUuid,
    is_guest: true
  };

  const token = generateToken(user);

  return {
    user,
    token
  };
}

/**
 * 用户注册（支持游客转正）
 */
async function registerUser(email, password, guestToken = null) {
  // 如果提供了游客token，尝试游客转正
  if (guestToken) {
    try {
      const decoded = verifyToken(guestToken);

      // 验证是否为游客用户
      const guestUsers = await query(
        'SELECT id, uuid, is_guest FROM users WHERE uuid = ? AND is_guest = 1',
        [decoded.uuid]
      );

      if (guestUsers.length > 0) {
        const guestUser = guestUsers[0];

        // 检查邮箱是否已被其他用户使用
        const existingUsers = await query(
          'SELECT COUNT(*) as count FROM users WHERE email = ? AND id != ?',
          [email, guestUser.id]
        );

        if (existingUsers[0].count > 0) {
          throw new Error('EMAIL_EXISTS');
        }

        // 游客转正：更新现有用户信息
        const passwordHash = await hashPassword(password);

        await query(
          'UPDATE users SET email = ?, password_hash = ?, is_guest = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [email, passwordHash, guestUser.id]
        );

        // 获取更新后的用户信息
        const users = await query(
          'SELECT uuid, email, is_guest, status, daily_message_count, message_credits FROM users WHERE id = ?',
          [guestUser.id]
        );

        const user = users[0];
        user.is_guest = Boolean(user.is_guest);

        // 生成新的JWT
        const token = generateToken({
          uuid: user.uuid,
          email: user.email,
          is_guest: user.is_guest
        });

        return {
          user,
          token,
          converted: true // 标识这是游客转正
        };
      }
    } catch (error) {
      // 如果游客token无效，继续正常注册流程
      console.warn('游客转正失败，继续正常注册:', error.message);
    }
  }

  // 正常注册流程
  // 检查邮箱是否已存在
  const existingUsers = await query(
    'SELECT id FROM users WHERE email = ?',
    [email]
  );

  if (existingUsers.length > 0) {
    throw new Error('EMAIL_EXISTS');
  }

  // 哈希密码
  const passwordHash = await hashPassword(password);
  const userUuid = uuidv4();

  // 创建用户
  await query(
    'INSERT INTO users (uuid, email, password_hash, is_guest) VALUES (?, ?, ?, ?)',
    [userUuid, email, passwordHash, false]
  );

  const user = {
    uuid: userUuid,
    email,
    is_guest: false
  };

  const token = generateToken(user);

  return {
    user,
    token,
    converted: false // 标识这是新注册
  };
}

/**
 * 用户登录
 */
async function loginUser(email, password) {
  // 查找用户
  const users = await query(
    'SELECT uuid, email, password_hash, is_guest, status FROM users WHERE email = ?',
    [email]
  );

  if (users.length === 0) {
    throw new Error('INVALID_CREDENTIALS');
  }

  const user = users[0];

  // 检查账户状态
  if (user.status !== 'active') {
    throw new Error('ACCOUNT_BANNED');
  }

  // 验证密码
  const isPasswordValid = await verifyPassword(password, user.password_hash);
  if (!isPasswordValid) {
    throw new Error('INVALID_CREDENTIALS');
  }

  const userInfo = {
    uuid: user.uuid,
    email: user.email,
    is_guest: Boolean(user.is_guest)
  };

  const token = generateToken(userInfo);

  return {
    user: userInfo,
    token
  };
}

/**
 * 根据UUID获取用户信息
 */
async function getUserByUuid(uuid) {
  const users = await query(
    'SELECT uuid, email, is_guest, status, daily_message_count, message_credits, profile_summary, created_at FROM users WHERE uuid = ?',
    [uuid]
  );

  if (users.length > 0) {
    const user = users[0];
    // 转换布尔值
    user.is_guest = Boolean(user.is_guest);
    return user;
  }

  return null;
}

module.exports = {
  createGuestUser,
  registerUser,
  loginUser,
  getUserByUuid
};
