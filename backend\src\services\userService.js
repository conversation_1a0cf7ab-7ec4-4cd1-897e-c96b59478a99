const { v4: uuidv4 } = require('uuid');
const { query } = require('../config/database');
const { hashPassword, verifyPassword } = require('../utils/password');
const { generateToken } = require('../utils/jwt');

/**
 * 创建游客用户
 */
async function createGuestUser() {
  const userUuid = uuidv4();
  
  await query(
    'INSERT INTO users (uuid, is_guest) VALUES (?, ?)',
    [userUuid, true]
  );

  const user = {
    uuid: userUuid,
    is_guest: true
  };

  const token = generateToken(user);

  return {
    user,
    token
  };
}

/**
 * 用户注册
 */
async function registerUser(email, password) {
  // 检查邮箱是否已存在
  const existingUsers = await query(
    'SELECT id FROM users WHERE email = ?',
    [email]
  );

  if (existingUsers.length > 0) {
    throw new Error('EMAIL_EXISTS');
  }

  // 哈希密码
  const passwordHash = await hashPassword(password);
  const userUuid = uuidv4();

  // 创建用户
  await query(
    'INSERT INTO users (uuid, email, password_hash, is_guest) VALUES (?, ?, ?, ?)',
    [userUuid, email, passwordHash, false]
  );

  const user = {
    uuid: userUuid,
    email,
    is_guest: false
  };

  const token = generateToken(user);

  return {
    user,
    token
  };
}

/**
 * 用户登录
 */
async function loginUser(email, password) {
  // 查找用户
  const users = await query(
    'SELECT uuid, email, password_hash, is_guest, role, status FROM users WHERE email = ?',
    [email]
  );

  if (users.length === 0) {
    throw new Error('INVALID_CREDENTIALS');
  }

  const user = users[0];

  // 检查账户状态
  if (user.status !== 'active') {
    throw new Error('ACCOUNT_BANNED');
  }

  // 验证密码
  const isPasswordValid = await verifyPassword(password, user.password_hash);
  if (!isPasswordValid) {
    throw new Error('INVALID_CREDENTIALS');
  }

  const userInfo = {
    uuid: user.uuid,
    email: user.email,
    is_guest: user.is_guest,
    role: user.role
  };

  const token = generateToken(userInfo);

  return {
    user: userInfo,
    token
  };
}

/**
 * 根据UUID获取用户信息
 */
async function getUserByUuid(uuid) {
  const users = await query(
    'SELECT uuid, email, is_guest, status, daily_message_count, message_credits, profile_summary, created_at FROM users WHERE uuid = ?',
    [uuid]
  );

  if (users.length > 0) {
    const user = users[0];
    // 转换布尔值
    user.is_guest = Boolean(user.is_guest);
    return user;
  }

  return null;
}

module.exports = {
  createGuestUser,
  registerUser,
  loginUser,
  getUserByUuid
};
