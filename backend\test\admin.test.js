const request = require('supertest');
const app = require('../src/app');

describe('管理员模块测试', () => {
  let adminToken;
  let userToken;
  let testUserUuid;
  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123456';
  const testEmail = `admin_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 管理员登录获取token
    const adminLoginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({
        email: adminEmail,
        password: adminPassword
      });

    if (adminLoginResponse.body.code === 200) {
      adminToken = adminLoginResponse.body.data.token;
    }

    // 创建测试用户
    const userRegisterResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (userRegisterResponse.body.code === 201) {
      userToken = userRegisterResponse.body.data.token;
      testUserUuid = userRegisterResponse.body.data.user.uuid;
    }
  });

  describe('GET /api/v1/admin/stats', () => {
    test('管理员应该能获取系统统计信息', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(response.body.data).toHaveProperty('users');
      expect(response.body.data).toHaveProperty('sessions');
      expect(response.body.data).toHaveProperty('messages');
      expect(response.body.data).toHaveProperty('orders');

      // 检查用户统计
      expect(response.body.data.users).toHaveProperty('total');
      expect(response.body.data.users).toHaveProperty('guest');
      expect(response.body.data.users).toHaveProperty('registered');
      expect(response.body.data.users).toHaveProperty('active');
      expect(response.body.data.users).toHaveProperty('banned');
      expect(response.body.data.users).toHaveProperty('today_new');
    });

    test('普通用户应该被拒绝访问', async () => {
      if (!userToken) {
        console.log('跳过测试：用户注册失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/stats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.code).toBe(40301);
      expect(response.body.message).toBe('需要管理员权限');
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .get('/api/v1/admin/stats')
        .expect(403);

      expect(response.body.code).toBe(40301);
    });
  });

  describe('GET /api/v1/admin/users', () => {
    test('管理员应该能获取用户列表', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('users');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.users)).toBe(true);

      // 检查分页信息
      expect(response.body.data.pagination).toHaveProperty('page');
      expect(response.body.data.pagination).toHaveProperty('limit');
      expect(response.body.data.pagination).toHaveProperty('total');
      expect(response.body.data.pagination).toHaveProperty('pages');

      // 检查用户数据结构
      if (response.body.data.users.length > 0) {
        const user = response.body.data.users[0];
        expect(user).toHaveProperty('uuid');
        expect(user).toHaveProperty('email');
        expect(user).toHaveProperty('is_guest');
        expect(user).toHaveProperty('role');
        expect(user).toHaveProperty('status');
      }
    });

    test('应该支持分页参数', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/users?page=1&limit=5')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(5);
      expect(response.body.data.users.length).toBeLessThanOrEqual(5);
    });

    test('应该支持搜索功能', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/admin/users?search=${adminEmail}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      // 应该能找到管理员账户
      const foundAdmin = response.body.data.users.find(user => user.email === adminEmail);
      expect(foundAdmin).toBeDefined();
      expect(foundAdmin.role).toBe('admin');
    });
  });

  describe('GET /api/v1/admin/users/:user_uuid', () => {
    test('管理员应该能获取用户详情', async () => {
      if (!adminToken || !testUserUuid) {
        console.log('跳过测试：管理员登录失败或测试用户不存在');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/admin/users/${testUserUuid}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('uuid', testUserUuid);
      expect(response.body.data).toHaveProperty('email', testEmail);
      expect(response.body.data).toHaveProperty('role', 'user');
      expect(response.body.data).toHaveProperty('status', 'active');
    });

    test('应该拒绝不存在的用户UUID', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      const response = await request(app)
        .get(`/api/v1/admin/users/${fakeUuid}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('用户不存在');
    });

    test('应该拒绝无效的UUID格式', async () => {
      if (!adminToken) {
        console.log('跳过测试：管理员登录失败');
        return;
      }

      const response = await request(app)
        .get('/api/v1/admin/users/invalid-uuid')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('PUT /api/v1/admin/users/:user_uuid/status', () => {
    test('管理员应该能更新用户状态', async () => {
      if (!adminToken || !testUserUuid) {
        console.log('跳过测试：管理员登录失败或测试用户不存在');
        return;
      }

      // 禁用用户
      const banResponse = await request(app)
        .put(`/api/v1/admin/users/${testUserUuid}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'banned'
        })
        .expect(200);

      expect(banResponse.body.code).toBe(200);
      expect(banResponse.body.message).toBe('User status updated successfully');

      // 恢复用户
      const activeResponse = await request(app)
        .put(`/api/v1/admin/users/${testUserUuid}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'active'
        })
        .expect(200);

      expect(activeResponse.body.code).toBe(200);
    });

    test('应该拒绝无效的状态值', async () => {
      if (!adminToken || !testUserUuid) {
        console.log('跳过测试：管理员登录失败或测试用户不存在');
        return;
      }

      const response = await request(app)
        .put(`/api/v1/admin/users/${testUserUuid}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          status: 'invalid_status'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/admin/users/:user_uuid/sessions', () => {
    test('管理员应该能获取用户的聊天会话', async () => {
      if (!adminToken || !testUserUuid) {
        console.log('跳过测试：管理员登录失败或测试用户不存在');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/admin/users/${testUserUuid}/sessions`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('GET /api/v1/admin/users/:user_uuid/orders', () => {
    test('管理员应该能获取用户的订单记录', async () => {
      if (!adminToken || !testUserUuid) {
        console.log('跳过测试：管理员登录失败或测试用户不存在');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/admin/users/${testUserUuid}/orders`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });
});
