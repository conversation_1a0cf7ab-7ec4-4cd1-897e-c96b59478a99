const request = require('supertest');
const app = require('../src/app');

describe('用户中心模块测试', () => {
  let userToken;
  let guestToken;
  let sessionUuid;
  const testEmail = `usercenter${Date.now()}@example.com`;
  const testPassword = 'test123456';
  const newPassword = 'newtest123456';

  beforeAll(async () => {
    // 创建测试用户并获取token
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;

    // 创建游客用户
    const guestResponse = await request(app)
      .post('/api/v1/auth/guest');

    guestToken = guestResponse.body.data.token;

    // 创建一个测试会话（为了测试会话列表）
    const rolesResponse = await request(app)
      .get('/api/v1/ai-roles');

    if (rolesResponse.body.data.length > 0) {
      const validRoleId = rolesResponse.body.data[0].id;
      
      const sessionResponse = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: validRoleId
        });

      sessionUuid = sessionResponse.body.data.uuid;
    }
  });

  describe('GET /api/v1/me', () => {
    test('应该成功获取当前用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(response.body.data).toHaveProperty('uuid');
      expect(response.body.data).toHaveProperty('email', testEmail);
      expect(response.body.data).toHaveProperty('is_guest', false);
      expect(response.body.data).toHaveProperty('status', 'active');
      expect(response.body.data).toHaveProperty('daily_message_count');
      expect(response.body.data).toHaveProperty('message_credits');
      expect(response.body.data).toHaveProperty('created_at');
    });

    test('游客用户也应该能获取用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/me')
        .set('Authorization', `Bearer ${guestToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data).toHaveProperty('is_guest', true);
      expect(response.body.data.email).toBeNull();
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .get('/api/v1/me')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });

    test('应该拒绝无效token', async () => {
      const response = await request(app)
        .get('/api/v1/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('GET /api/v1/me/chats', () => {
    test('应该成功获取用户的聊天会话列表', async () => {
      const response = await request(app)
        .get('/api/v1/me/chats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(Array.isArray(response.body.data)).toBe(true);

      // 如果有会话，检查数据结构
      if (response.body.data.length > 0) {
        const session = response.body.data[0];
        expect(session).toHaveProperty('uuid');
        expect(session).toHaveProperty('title');
        expect(session).toHaveProperty('ai_role_id');
        expect(session).toHaveProperty('ai_role_name');
        expect(session).toHaveProperty('updated_at');
      }
    });

    test('游客用户也应该能获取会话列表', async () => {
      const response = await request(app)
        .get('/api/v1/me/chats')
        .set('Authorization', `Bearer ${guestToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .get('/api/v1/me/chats')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('PUT /api/v1/me/password', () => {
    test('应该成功修改密码', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          old_password: testPassword,
          new_password: newPassword
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Password changed successfully');
      expect(response.body.data).toBeNull();
    });

    test('应该能用新密码登录', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testEmail,
          password: newPassword
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Login successful');
    });

    test('应该拒绝错误的旧密码', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          old_password: 'wrongpassword',
          new_password: 'anothernewpassword123'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
      expect(response.body.message).toBe('旧密码错误');
    });

    test('应该拒绝新密码与旧密码相同', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          old_password: newPassword,
          new_password: newPassword
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
      expect(response.body.message).toBe('新密码不能与旧密码相同');
    });

    test('应该拒绝游客用户修改密码', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .set('Authorization', `Bearer ${guestToken}`)
        .send({
          old_password: 'anypassword',
          new_password: 'newpassword123'
        })
        .expect(403);

      expect(response.body.code).toBe(40301);
      expect(response.body.message).toBe('游客用户无法修改密码，请先注册');
    });

    test('应该拒绝无效的新密码格式', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          old_password: newPassword,
          new_password: '123'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝缺少旧密码', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          new_password: 'validpassword123'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .put('/api/v1/me/password')
        .send({
          old_password: newPassword,
          new_password: 'anotherpassword123'
        })
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });
});
