const { v4: uuidv4 } = require('uuid');
const { query } = require('../config/database');

/**
 * 获取所有可用套餐
 */
async function getAllPlans() {
  const plans = await query(
    'SELECT id, name, description, price, plan_type, message_credits, duration_days FROM plans WHERE is_active = ? ORDER BY id ASC',
    [true]
  );

  return plans;
}

/**
 * 根据ID获取套餐信息
 */
async function getPlanById(planId) {
  const plans = await query(
    'SELECT id, name, description, price, plan_type, message_credits, duration_days FROM plans WHERE id = ? AND is_active = ?',
    [planId, true]
  );

  return plans.length > 0 ? plans[0] : null;
}

/**
 * 创建订单
 */
async function createOrder(userId, planId) {
  // 检查套餐是否存在且启用
  const plan = await getPlanById(planId);
  if (!plan) {
    throw new Error('PLAN_NOT_FOUND');
  }

  const orderUuid = uuidv4();
  const amount = plan.price;

  // 创建订单记录
  await query(
    'INSERT INTO orders (uuid, user_id, plan_id, amount, status, payment_gateway) VALUES (?, ?, ?, ?, ?, ?)',
    [orderUuid, userId, planId, amount, 'pending', 'mock_payment']
  );

  // 模拟支付网关返回的支付链接
  const paymentDetails = {
    pay_url: `https://mock-payment.example.com/pay?order=${orderUuid}&amount=${amount}`,
    qr_code: `https://mock-payment.example.com/qr?order=${orderUuid}`,
    expire_time: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30分钟后过期
  };

  return {
    order_uuid: orderUuid,
    status: 'pending',
    amount: amount.toString(),
    payment_details: paymentDetails
  };
}

/**
 * 处理支付通知（模拟）
 */
async function processPaymentNotify(orderUuid, status, transactionId = null, gateway = 'mock_payment') {
  // 查找订单
  const orders = await query(
    'SELECT o.id, o.user_id, o.plan_id, o.amount, o.status, p.plan_type, p.message_credits, p.duration_days ' +
    'FROM orders o JOIN plans p ON o.plan_id = p.id WHERE o.uuid = ?',
    [orderUuid]
  );

  if (orders.length === 0) {
    throw new Error('ORDER_NOT_FOUND');
  }

  const order = orders[0];

  // 检查订单状态
  if (order.status !== 'pending') {
    throw new Error('ORDER_ALREADY_PROCESSED');
  }

  const newStatus = status === 'success' ? 'completed' : 'failed';

  // 更新订单状态
  await query(
    'UPDATE orders SET status = ?, gateway_transaction_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [newStatus, transactionId, order.id]
  );

  // 如果支付成功，处理用户权益
  if (status === 'success') {
    await processSuccessfulPayment(order);
  }

  return {
    order_uuid: orderUuid,
    status: newStatus,
    processed: true
  };
}

/**
 * 处理支付成功后的用户权益
 */
async function processSuccessfulPayment(order) {
  const { user_id, plan_id, id: order_id, plan_type, message_credits, duration_days } = order;

  if (plan_type === 'one_time') {
    // 一次性购买：增加消息点数
    await query(
      'UPDATE users SET message_credits = message_credits + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [message_credits, user_id]
    );
  } else if (plan_type === 'subscription') {
    // 订阅：创建订阅记录
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + duration_days * 24 * 60 * 60 * 1000);

    await query(
      'INSERT INTO user_subscriptions (user_id, plan_id, order_id, start_date, end_date, status) VALUES (?, ?, ?, ?, ?, ?)',
      [user_id, plan_id, order_id, startDate, endDate, 'active']
    );
  }
}

/**
 * 获取用户订阅信息
 */
async function getUserSubscriptions(userId) {
  const subscriptions = await query(
    'SELECT us.start_date, us.end_date, us.status, p.name as plan_name ' +
    'FROM user_subscriptions us ' +
    'JOIN plans p ON us.plan_id = p.id ' +
    'WHERE us.user_id = ? AND us.status = "active" AND us.end_date > NOW() ' +
    'ORDER BY us.end_date DESC',
    [userId]
  );

  return subscriptions;
}

/**
 * 检查用户是否有有效订阅
 */
async function hasActiveSubscription(userId) {
  const subscriptions = await query(
    'SELECT COUNT(*) as count FROM user_subscriptions ' +
    'WHERE user_id = ? AND status = "active" AND end_date > NOW()',
    [userId]
  );

  return subscriptions[0].count > 0;
}

/**
 * 获取用户订单历史
 */
async function getUserOrders(userId) {
  const orders = await query(
    'SELECT o.uuid, o.amount, o.status, o.created_at, p.name as plan_name ' +
    'FROM orders o ' +
    'JOIN plans p ON o.plan_id = p.id ' +
    'WHERE o.user_id = ? ' +
    'ORDER BY o.created_at DESC',
    [userId]
  );

  return orders;
}

module.exports = {
  getAllPlans,
  getPlanById,
  createOrder,
  processPaymentNotify,
  getUserSubscriptions,
  hasActiveSubscription,
  getUserOrders
};
