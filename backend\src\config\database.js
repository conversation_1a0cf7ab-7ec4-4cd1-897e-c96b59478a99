const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'ai_companion',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 初始化数据库和表
async function initDatabase() {
  let connection;
  try {
    // 先连接到MySQL服务器（不指定数据库）
    const tempConfig = { ...dbConfig };
    delete tempConfig.database;
    connection = await mysql.createConnection(tempConfig);
    
    // 创建数据库（如果不存在）
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`数据库 ${dbConfig.database} 已创建或已存在`);
    
    await connection.end();
    
    // 重新连接到指定数据库
    connection = await mysql.createConnection(dbConfig);
    
    // 创建用户表
    const createUsersTable = `
      CREATE TABLE IF NOT EXISTS \`users\` (
        \`id\` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户内部ID',
        \`uuid\` CHAR(36) NOT NULL COMMENT '用户公开ID',
        \`email\` VARCHAR(255) NULL COMMENT '邮箱',
        \`password_hash\` VARCHAR(255) NULL COMMENT '加盐哈希后的密码',
        \`is_guest\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否为游客账户',
        \`status\` ENUM('active', 'banned') NOT NULL DEFAULT 'active' COMMENT '账户状态',
        \`daily_message_count\` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日消息使用计数',
        \`last_usage_date\` DATE NULL COMMENT '上次使用日期，用于重置每日计数',
        \`message_credits\` INT NOT NULL DEFAULT 0 COMMENT '剩余消息点数/条数',
        \`profile_summary\` TEXT NULL COMMENT '用户长期记忆摘要',
        \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_uuid\` (\`uuid\`),
        UNIQUE KEY \`uk_email\` (\`email\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
    `;
    
    await connection.execute(createUsersTable);
    console.log('用户表已创建或已存在');
    
    await connection.end();
    console.log('数据库初始化完成');
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    if (connection) {
      await connection.end();
    }
    throw error;
  }
}

// 获取数据库连接
async function getConnection() {
  return await pool.getConnection();
}

// 执行查询
async function query(sql, params = []) {
  const connection = await getConnection();
  try {
    const [rows] = await connection.execute(sql, params);
    return rows;
  } finally {
    connection.release();
  }
}

module.exports = {
  pool,
  getConnection,
  query,
  initDatabase
};
