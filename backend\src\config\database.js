const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'ai_companion',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 初始化数据库和表
async function initDatabase() {
  let connection;
  try {
    // 先连接到MySQL服务器（不指定数据库）
    const tempConfig = { ...dbConfig };
    delete tempConfig.database;
    connection = await mysql.createConnection(tempConfig);
    
    // 创建数据库（如果不存在）
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`数据库 ${dbConfig.database} 已创建或已存在`);
    
    await connection.end();
    
    // 重新连接到指定数据库
    connection = await mysql.createConnection(dbConfig);
    
    // 创建用户表
    const createUsersTable = `
      CREATE TABLE IF NOT EXISTS \`users\` (
        \`id\` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户内部ID',
        \`uuid\` CHAR(36) NOT NULL COMMENT '用户公开ID',
        \`email\` VARCHAR(255) NULL COMMENT '邮箱',
        \`password_hash\` VARCHAR(255) NULL COMMENT '加盐哈希后的密码',
        \`is_guest\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否为游客账户',
        \`status\` ENUM('active', 'banned') NOT NULL DEFAULT 'active' COMMENT '账户状态',
        \`daily_message_count\` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日消息使用计数',
        \`last_usage_date\` DATE NULL COMMENT '上次使用日期，用于重置每日计数',
        \`message_credits\` INT NOT NULL DEFAULT 0 COMMENT '剩余消息点数/条数',
        \`profile_summary\` TEXT NULL COMMENT '用户长期记忆摘要',
        \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_uuid\` (\`uuid\`),
        UNIQUE KEY \`uk_email\` (\`email\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
    `;
    
    await connection.execute(createUsersTable);
    console.log('用户表已创建或已存在');

    // 创建AI角色表
    const createAiRolesTable = `
      CREATE TABLE IF NOT EXISTS \`ai_roles\` (
        \`id\` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'AI角色ID',
        \`name\` VARCHAR(100) NOT NULL COMMENT '角色名称',
        \`avatar_url\` VARCHAR(255) NULL COMMENT '角色头像URL',
        \`description\` VARCHAR(500) NULL COMMENT '角色一句话简介',
        \`details\` TEXT NULL COMMENT '角色详细介绍',
        \`system_prompt\` TEXT NOT NULL COMMENT '给AI的核心人设指令(System Prompt)',
        \`is_active\` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用该角色',
        \`created_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色配置表'
    `;

    await connection.execute(createAiRolesTable);
    console.log('AI角色表已创建或已存在');

    await connection.end();
    console.log('数据库初始化完成');
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    if (connection) {
      await connection.end();
    }
    throw error;
  }
}

// 获取数据库连接
async function getConnection() {
  return await pool.getConnection();
}

// 执行查询
async function query(sql, params = []) {
  const connection = await getConnection();
  try {
    const [rows] = await connection.execute(sql, params);
    return rows;
  } finally {
    connection.release();
  }
}

// 初始化AI角色数据
async function initAiRoles() {
  try {
    // 检查是否已有数据
    const existingRoles = await query('SELECT COUNT(*) as count FROM ai_roles');
    if (existingRoles[0].count > 0) {
      console.log('AI角色数据已存在，跳过初始化');
      return;
    }

    // 插入示例AI角色数据
    const sampleRoles = [
      {
        name: '博学的历史学家',
        avatar_url: 'https://example.com/avatars/historian.png',
        description: '精通世界历史，能与你深入探讨任何历史事件。',
        details: '他是一位沉浸在时间长河中的学者，书房里堆满了古籍和地图。他不仅知道重大事件的始末，更了解那些不为人知的历史细节。无论是古代文明的兴衰，还是近现代的变革，他都能为你娓娓道来，让历史变得生动有趣。',
        system_prompt: '你是一位博学的历史学家，拥有深厚的历史知识和丰富的学术经验。你善于用生动有趣的方式讲述历史事件，能够将复杂的历史背景用通俗易懂的语言解释清楚。你对各个时代的文化、政治、经济都有深入的了解，总是能够提供独特的历史视角和深刻的见解。'
      },
      {
        name: '温柔的心理顾问',
        avatar_url: 'https://example.com/avatars/counselor.png',
        description: '倾听你的烦恼，给予你温暖的支持和专业的建议。',
        details: '她是一位经验丰富的心理咨询师，拥有温暖的笑容和耐心的倾听能力。她深知每个人内心的复杂性，总是能够用最温和的方式帮助你理解自己的情感，找到内心的平静。无论你面临什么困扰，她都会陪伴在你身边。',
        system_prompt: '你是一位温柔、专业的心理顾问，具有丰富的心理咨询经验。你善于倾听，总是以同理心和包容的态度对待每一个来访者。你会用温暖的语言提供情感支持，帮助人们理解自己的情感，提供实用的心理调节建议，但不会进行医学诊断。你的目标是让每个人都能感受到被理解和支持。'
      },
      {
        name: '活泼的生活助手',
        avatar_url: 'https://example.com/avatars/assistant.png',
        description: '帮你解决生活中的各种问题，让每一天都充满活力。',
        details: '她是一个充满活力的生活达人，对各种生活技巧和实用知识了如指掌。从烹饪美食到整理收纳，从健康养生到时间管理，她总能给出最实用的建议。她的热情和正能量会感染每一个人，让生活变得更加美好。',
        system_prompt: '你是一位活泼开朗的生活助手，对生活的方方面面都很有经验。你热爱生活，总是充满正能量，善于发现生活中的美好。你会提供实用的生活建议，包括但不限于烹饪、健康、时间管理、人际关系等。你的语言风格轻松愉快，总是能够鼓励和激励他人。'
      },
      {
        name: '理性的分析师',
        avatar_url: 'https://example.com/avatars/analyst.png',
        description: '用逻辑和数据帮你分析问题，找到最优解决方案。',
        details: '他是一位逻辑思维极强的分析专家，擅长用理性的方法分析复杂问题。他总是能够从多个角度审视问题，运用数据和逻辑推理找出最优的解决方案。无论是工作决策还是人生选择，他都能为你提供清晰的分析框架。',
        system_prompt: '你是一位理性、逻辑思维强的分析师，擅长用系统性的方法分析问题。你会从多个维度考虑问题，运用逻辑推理和数据分析来提供建议。你的回答总是条理清晰、有理有据，善于将复杂问题分解为可管理的部分。你注重客观性和实用性，但也会考虑人文因素。'
      }
    ];

    for (const role of sampleRoles) {
      await query(
        'INSERT INTO ai_roles (name, avatar_url, description, details, system_prompt) VALUES (?, ?, ?, ?, ?)',
        [role.name, role.avatar_url, role.description, role.details, role.system_prompt]
      );
    }

    console.log('AI角色示例数据初始化完成');
  } catch (error) {
    console.error('AI角色数据初始化失败:', error);
    throw error;
  }
}

module.exports = {
  pool,
  getConnection,
  query,
  initDatabase,
  initAiRoles
};
