const paymentService = require('../services/paymentService');

/**
 * 获取所有可用套餐
 */
async function getPlans(req, res, next) {
  try {
    const plans = await paymentService.getAllPlans();
    
    res.success(plans, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 创建订单
 */
async function createOrder(req, res, next) {
  try {
    const { plan_id } = req.body;
    const userId = req.user.id;

    const orderResult = await paymentService.createOrder(userId, plan_id);
    
    res.success(orderResult, 'Order created successfully', 201);
  } catch (error) {
    if (error.message === 'PLAN_NOT_FOUND') {
      return res.error('套餐不存在或已禁用', 40401, 404);
    }
    next(error);
  }
}

/**
 * 支付结果通知（模拟）
 */
async function paymentNotify(req, res, next) {
  try {
    const { order_uuid, status, transaction_id, gateway } = req.body;

    const result = await paymentService.processPaymentNotify(
      order_uuid, 
      status, 
      transaction_id, 
      gateway
    );
    
    // 支付通知接口通常返回简单的成功标识
    res.status(200).send('SUCCESS');
  } catch (error) {
    if (error.message === 'ORDER_NOT_FOUND') {
      return res.status(404).send('ORDER_NOT_FOUND');
    }
    if (error.message === 'ORDER_ALREADY_PROCESSED') {
      return res.status(400).send('ORDER_ALREADY_PROCESSED');
    }
    
    console.error('支付通知处理失败:', error);
    res.status(500).send('FAILED');
  }
}

/**
 * 获取用户订阅信息
 */
async function getUserSubscriptions(req, res, next) {
  try {
    const userId = req.user.id;

    const subscriptions = await paymentService.getUserSubscriptions(userId);
    
    res.success(subscriptions, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取用户订单历史
 */
async function getUserOrders(req, res, next) {
  try {
    const userId = req.user.id;

    const orders = await paymentService.getUserOrders(userId);
    
    res.success(orders, 'Success');
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getPlans,
  createOrder,
  paymentNotify,
  getUserSubscriptions,
  getUserOrders
};
