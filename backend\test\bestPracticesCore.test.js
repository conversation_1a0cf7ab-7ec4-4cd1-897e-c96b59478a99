const request = require('supertest');
const app = require('../src/app');
const { batchResetDailyQuota } = require('../src/utils/dailyQuota');
const { sanitizeText, validateEmail, validatePassword } = require('../src/utils/security');
const { createPagination } = require('../src/utils/pagination');

describe('API最佳实践核心功能测试', () => {
  let userToken;
  const testEmail = `best_practices_core_${Date.now()}@example.com`;
  const testPassword = 'TestPassword123';

  describe('1. 每日额度重置功能', () => {
    test('每日额度重置脚本应该正常工作', async () => {
      const resetCount = await batchResetDailyQuota();
      expect(typeof resetCount).toBe('number');
      expect(resetCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('2. 安全性验证', () => {
    test('输入清理功能应该正常工作', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const cleaned = sanitizeText(maliciousInput, { allowSpecialChars: false });
      
      expect(cleaned).not.toContain('<script>');
      expect(cleaned).not.toContain('</script>');
      expect(cleaned).toContain('Hello World');
    });

    test('邮箱验证应该正确工作', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('')).toBe(false);
      expect(validateEmail(null)).toBe(false);
    });

    test('密码强度验证应该正确工作', () => {
      const strongPassword = validatePassword('StrongPass123');
      expect(strongPassword.valid).toBe(true);
      expect(strongPassword.errors).toHaveLength(0);

      const weakPassword = validatePassword('123');
      expect(weakPassword.valid).toBe(false);
      expect(weakPassword.errors.length).toBeGreaterThan(0);
    });

    test('应该拒绝过大的请求', async () => {
      const largeContent = 'A'.repeat(10000); // 10KB内容

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: '<EMAIL>',
          password: largeContent
        });

      // 应该被输入清理或验证拒绝
      expect(response.status).toBeGreaterThanOrEqual(400);
    });
  });

  describe('3. 分页功能', () => {
    test('分页工具应该正确计算分页参数', () => {
      const pagination = createPagination({ page: 2, limit: 10 });
      
      expect(pagination.page).toBe(2);
      expect(pagination.limit).toBe(10);
      expect(pagination.offset).toBe(10);
    });

    test('分页应该有合理的限制', () => {
      const pagination = createPagination({ page: 1, limit: 1000 }, { maxLimit: 100 });
      
      expect(pagination.limit).toBe(100); // 应该被限制到最大值
    });

    test('分页元数据应该正确计算', () => {
      const pagination = createPagination({ page: 2, limit: 10 });
      const metadata = pagination.getMetadata(25);
      
      expect(metadata.totalPages).toBe(3);
      expect(metadata.hasNext).toBe(true);
      expect(metadata.hasPrev).toBe(true);
      expect(metadata.nextPage).toBe(3);
      expect(metadata.prevPage).toBe(1);
    });
  });

  describe('4. 用户注册和登录', () => {
    test('用户应该能够正常注册', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: testEmail,
          password: testPassword
        });

      expect(response.status).toBeLessThan(400);
      
      if (response.body.data && response.body.data.token) {
        userToken = response.body.data.token;
        expect(response.body.data.user.email).toBe(testEmail);
      }
    });

    test('注册后的用户应该能够正常登录', async () => {
      if (!userToken) {
        console.log('跳过测试：用户未注册');
        return;
      }

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data.user.email).toBe(testEmail);
    });

    test('应该拒绝重复的邮箱注册', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: testEmail,
          password: testPassword
        });

      expect(response.status).toBeGreaterThanOrEqual(400);
    });
  });

  describe('5. 聊天功能集成', () => {
    test('用户应该能够创建聊天会话', async () => {
      if (!userToken) {
        console.log('跳过测试：用户未登录');
        return;
      }

      // 获取AI角色
      const rolesResponse = await request(app)
        .get('/api/v1/ai-roles');

      if (rolesResponse.body.data && rolesResponse.body.data.length > 0) {
        const validRoleId = rolesResponse.body.data[0].id;

        // 创建会话
        const sessionResponse = await request(app)
          .post('/api/v1/chat/sessions')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            ai_role_id: validRoleId
          });

        expect(sessionResponse.status).toBeLessThan(400);
        
        if (sessionResponse.body.data) {
          expect(sessionResponse.body.data).toHaveProperty('uuid');
        }
      }
    });

    test('获取会话列表应该支持分页', async () => {
      if (!userToken) {
        console.log('跳过测试：用户未登录');
        return;
      }

      const response = await request(app)
        .get('/api/v1/chat/sessions?page=1&limit=5')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('6. 错误处理', () => {
    test('应该正确处理无效的认证token', async () => {
      const response = await request(app)
        .get('/api/v1/chat/sessions')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });

    test('应该正确处理缺失的认证token', async () => {
      const response = await request(app)
        .get('/api/v1/chat/sessions')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });
});
