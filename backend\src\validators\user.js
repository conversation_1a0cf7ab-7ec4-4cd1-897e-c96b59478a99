const Joi = require('joi');

/**
 * 修改密码验证
 */
const changePasswordSchema = Joi.object({
  old_password: Joi.string()
    .required()
    .messages({
      'any.required': '旧密码是必填项'
    }),
  new_password: Joi.string()
    .min(6)
    .max(128)
    .pattern(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .required()
    .messages({
      'string.min': '新密码长度至少6个字符',
      'string.max': '新密码长度不能超过128个字符',
      'string.pattern.base': '新密码必须包含字母和数字',
      'any.required': '新密码是必填项'
    })
});

/**
 * 会话UUID验证
 */
const sessionUuidSchema = Joi.object({
  session_uuid: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': '会话ID格式无效',
      'any.required': '会话ID是必填项'
    })
});

/**
 * 验证中间件工厂函数
 */
function validate(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

/**
 * 验证路径参数中间件工厂函数
 */
function validateParams(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.params);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

module.exports = {
  changePasswordSchema,
  sessionUuidSchema,
  validate,
  validateParams
};
