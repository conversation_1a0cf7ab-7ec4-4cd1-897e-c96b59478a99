# AI伙伴聊天应用 - 项目结构说明

## 📁 项目目录结构

```
backend/
├── src/                          # 源代码目录
│   ├── config/                   # 配置文件
│   │   ├── database.js          # 数据库配置和初始化
│   │   └── llm.js               # LLM服务配置
│   │
│   ├── controllers/              # 控制器层
│   │   ├── authController.js    # 认证控制器
│   │   ├── chatController.js    # 聊天控制器
│   │   ├── userController.js    # 用户控制器
│   │   ├── aiRoleController.js  # AI角色控制器
│   │   ├── planController.js    # 套餐控制器
│   │   ├── orderController.js   # 订单控制器
│   │   └── adminController.js   # 管理员控制器
│   │
│   ├── middleware/               # 中间件
│   │   ├── auth.js              # 认证中间件
│   │   ├── admin.js             # 管理员权限中间件
│   │   ├── responseFormatter.js # 响应格式化中间件
│   │   ├── errorHandler.js      # 错误处理中间件
│   │   └── security.js          # 安全中间件
│   │
│   ├── routes/                   # 路由定义
│   │   ├── auth.js              # 认证路由
│   │   ├── chat.js              # 聊天路由
│   │   ├── user.js              # 用户路由
│   │   ├── aiRole.js            # AI角色路由
│   │   ├── plan.js              # 套餐路由
│   │   ├── order.js             # 订单路由
│   │   ├── admin.js             # 管理员路由
│   │   └── health.js            # 健康检查路由
│   │
│   ├── services/                 # 业务逻辑层
│   │   ├── userService.js       # 用户服务
│   │   ├── chatService.js       # 聊天服务
│   │   ├── aiRoleService.js     # AI角色服务
│   │   ├── planService.js       # 套餐服务
│   │   ├── orderService.js      # 订单服务
│   │   ├── paymentService.js    # 支付服务
│   │   ├── llmService.js        # LLM服务
│   │   └── adminService.js      # 管理员服务
│   │
│   ├── utils/                    # 工具函数
│   │   ├── jwt.js               # JWT工具
│   │   ├── password.js          # 密码处理工具
│   │   ├── security.js          # 安全工具
│   │   ├── dailyQuota.js        # 每日额度工具
│   │   ├── pagination.js        # 分页工具
│   │   ├── messageQueue.js      # 消息队列工具
│   │   ├── logger.js            # 日志工具
│   │   └── cache.js             # 缓存工具
│   │
│   ├── validators/               # 数据验证
│   │   ├── auth.js              # 认证验证
│   │   ├── chat.js              # 聊天验证
│   │   ├── user.js              # 用户验证
│   │   ├── aiRole.js            # AI角色验证
│   │   ├── plan.js              # 套餐验证
│   │   ├── order.js             # 订单验证
│   │   └── admin.js             # 管理员验证
│   │
│   ├── scripts/                  # 脚本文件
│   │   ├── dailyQuotaReset.js   # 每日额度重置脚本
│   │   ├── cleanupSessions.js   # 清理过期会话脚本
│   │   └── dbBackup.js          # 数据库备份脚本
│   │
│   └── app.js                    # 应用入口文件
│
├── test/                         # 测试文件
│   ├── auth.test.js             # 认证测试
│   ├── chat.test.js             # 聊天测试
│   ├── chatDeletion.test.js     # 聊天删除测试
│   ├── aiRole.test.js           # AI角色测试
│   ├── admin.test.js            # 管理员测试
│   ├── planManagement.test.js   # 套餐管理测试
│   ├── bestPractices.test.js    # 最佳实践测试
│   └── bestPracticesCore.test.js # 核心最佳实践测试
│
├── docs/                         # 文档目录
│   ├── MODULE_1_API_DOCUMENTATION.md      # 模块一文档
│   ├── MODULE_2_API_DOCUMENTATION.md      # 模块二文档
│   ├── MODULE_3_API_DOCUMENTATION.md      # 模块三文档
│   ├── MODULE_4_API_DOCUMENTATION.md      # 模块四文档
│   ├── MODULE_5_API_DOCUMENTATION.md      # 模块五文档
│   ├── MODULE_6_EXTENDED_API_DOCUMENTATION.md # 模块六文档
│   ├── CHAT_DELETION_API_DOCUMENTATION.md # 聊天删除文档
│   ├── PLAN_MANAGEMENT_API_DOCUMENTATION.md # 套餐管理文档
│   └── API_BEST_PRACTICES_DOCUMENTATION.md # 最佳实践文档
│
├── logs/                         # 日志文件目录
│   ├── error.log                # 错误日志
│   ├── combined.log             # 综合日志
│   └── access.log               # 访问日志
│
├── .env.example                  # 环境变量示例
├── .gitignore                   # Git忽略文件
├── package.json                 # 项目依赖配置
├── package-lock.json            # 依赖锁定文件
├── Dockerfile                   # Docker配置
├── docker-compose.yml           # Docker Compose配置
├── nginx.conf                   # Nginx配置
├── README.md                    # 项目说明
└── PROJECT_STRUCTURE.md         # 项目结构说明（本文件）
```

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────┐
│   Routes Layer  │  路由层 - 定义API端点
├─────────────────┤
│ Controller Layer│  控制器层 - 处理HTTP请求/响应
├─────────────────┤
│  Service Layer  │  服务层 - 业务逻辑处理
├─────────────────┤
│   Utils Layer   │  工具层 - 通用工具函数
├─────────────────┤
│ Database Layer  │  数据层 - 数据库操作
└─────────────────┘
```

### 中间件流水线
```
Request → Security → Auth → Validation → Controller → Service → Database
                                                    ↓
Response ← Formatter ← Error Handler ← Business Logic ← Data Access
```

## 📋 核心模块说明

### 1. 配置模块 (`src/config/`)
- **database.js**: 数据库连接配置、表结构初始化
- **llm.js**: LLM服务配置、API密钥管理

### 2. 控制器模块 (`src/controllers/`)
- 处理HTTP请求和响应
- 调用相应的服务层方法
- 统一的错误处理和响应格式

### 3. 中间件模块 (`src/middleware/`)
- **auth.js**: JWT认证、用户身份验证
- **admin.js**: 管理员权限检查
- **security.js**: 安全防护（频率限制、输入清理）
- **responseFormatter.js**: 统一响应格式
- **errorHandler.js**: 全局错误处理

### 4. 路由模块 (`src/routes/`)
- 定义API端点和HTTP方法
- 应用相应的中间件
- 参数验证和权限控制

### 5. 服务模块 (`src/services/`)
- 核心业务逻辑实现
- 数据库操作封装
- 第三方服务集成

### 6. 工具模块 (`src/utils/`)
- **jwt.js**: JWT令牌生成和验证
- **security.js**: 安全相关工具函数
- **dailyQuota.js**: 每日额度管理
- **pagination.js**: 分页查询工具
- **messageQueue.js**: 异步任务队列

### 7. 验证模块 (`src/validators/`)
- 使用Joi进行数据验证
- 统一的验证规则定义
- 自定义验证中间件

## 🔄 数据流向

### 典型API请求流程
```
1. 客户端发送请求
   ↓
2. Nginx反向代理（生产环境）
   ↓
3. Express应用接收请求
   ↓
4. 安全中间件（频率限制、输入清理）
   ↓
5. 认证中间件（JWT验证）
   ↓
6. 路由匹配
   ↓
7. 参数验证中间件
   ↓
8. 控制器处理
   ↓
9. 服务层业务逻辑
   ↓
10. 数据库操作
    ↓
11. 响应格式化
    ↓
12. 返回给客户端
```

### 错误处理流程
```
任何层级发生错误
   ↓
全局错误处理中间件
   ↓
错误日志记录
   ↓
统一错误响应格式
   ↓
返回给客户端
```

## 🗄️ 数据库设计

### 核心表结构
- **users**: 用户信息表
- **ai_roles**: AI角色配置表
- **chat_sessions**: 聊天会话表
- **chat_messages**: 聊天消息表
- **plans**: 套餐配置表
- **orders**: 订单记录表
- **user_subscriptions**: 用户订阅表

### 关系设计
```
users (1) ←→ (N) chat_sessions
chat_sessions (1) ←→ (N) chat_messages
users (1) ←→ (N) orders
plans (1) ←→ (N) orders
users (1) ←→ (N) user_subscriptions
ai_roles (1) ←→ (N) chat_sessions
```

## 🧪 测试架构

### 测试分类
- **单元测试**: 工具函数、服务层方法
- **集成测试**: API端点、数据库操作
- **端到端测试**: 完整业务流程

### 测试工具
- **Jest**: 测试框架
- **Supertest**: HTTP接口测试
- **数据库**: 测试专用数据库

## 🚀 部署架构

### 开发环境
```
Developer → Node.js + Express → MySQL
```

### 生产环境
```
Client → Nginx → Load Balancer → Node.js Cluster → MySQL Master/Slave
                                      ↓
                                   Redis Cache
                                      ↓
                                 Message Queue
```

## 📊 监控体系

### 应用监控
- **健康检查**: `/api/v1/health`
- **性能指标**: Prometheus集成
- **错误追踪**: Winston日志系统

### 基础设施监控
- **服务器资源**: CPU、内存、磁盘
- **数据库性能**: 查询时间、连接数
- **网络状况**: 响应时间、吞吐量

## 🔧 开发工具

### 代码质量
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理

### 调试工具
- **Node.js Inspector**: 调试器
- **Winston**: 日志系统
- **Postman**: API测试

这个项目结构设计遵循了现代Node.js应用的最佳实践，具有良好的可维护性、可扩展性和可测试性。
