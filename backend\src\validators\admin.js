const Joi = require('joi');

/**
 * 用户状态更新验证
 */
const updateUserStatusSchema = Joi.object({
  status: Joi.string()
    .valid('active', 'banned')
    .required()
    .messages({
      'any.only': '状态只能是active或banned',
      'any.required': '状态是必填项'
    })
});

/**
 * 用户UUID验证
 */
const userUuidSchema = Joi.object({
  user_uuid: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': '用户ID格式无效',
      'any.required': '用户ID是必填项'
    })
});

/**
 * 分页查询验证
 */
const paginationSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码必须大于0'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量必须大于0',
      'number.max': '每页数量不能超过100'
    }),
  search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': '搜索关键词长度不能超过100个字符'
    })
});

/**
 * 验证中间件工厂函数
 */
function validate(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

/**
 * 验证路径参数中间件工厂函数
 */
function validateParams(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.params);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

/**
 * 验证查询参数中间件工厂函数
 */
function validateQuery(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    // 将验证后的值赋回req.query
    req.query = value;
    next();
  };
}

/**
 * AI角色创建验证
 */
const createAiRoleSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': '角色名称不能为空',
      'string.max': '角色名称长度不能超过100个字符',
      'any.required': '角色名称是必填项'
    }),
  avatar_url: Joi.string()
    .uri()
    .max(255)
    .optional()
    .allow('')
    .messages({
      'string.uri': '头像URL格式无效',
      'string.max': '头像URL长度不能超过255个字符'
    }),
  description: Joi.string()
    .max(500)
    .optional()
    .allow('')
    .messages({
      'string.max': '角色简介长度不能超过500个字符'
    }),
  details: Joi.string()
    .optional()
    .allow('')
    .messages({}),
  system_prompt: Joi.string()
    .min(1)
    .required()
    .messages({
      'string.min': '系统提示词不能为空',
      'any.required': '系统提示词是必填项'
    })
});

/**
 * AI角色更新验证
 */
const updateAiRoleSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'string.min': '角色名称不能为空',
      'string.max': '角色名称长度不能超过100个字符'
    }),
  avatar_url: Joi.string()
    .uri()
    .max(255)
    .optional()
    .allow('')
    .messages({
      'string.uri': '头像URL格式无效',
      'string.max': '头像URL长度不能超过255个字符'
    }),
  description: Joi.string()
    .max(500)
    .optional()
    .allow('')
    .messages({
      'string.max': '角色简介长度不能超过500个字符'
    }),
  details: Joi.string()
    .optional()
    .allow('')
    .messages({}),
  system_prompt: Joi.string()
    .min(1)
    .optional()
    .messages({
      'string.min': '系统提示词不能为空'
    })
});

/**
 * AI角色ID验证
 */
const aiRoleIdSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'AI角色ID必须是数字',
      'number.integer': 'AI角色ID必须是整数',
      'number.positive': 'AI角色ID必须是正数',
      'any.required': 'AI角色ID是必填项'
    })
});

/**
 * 订单查询验证
 */
const orderQuerySchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码必须大于0'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量必须大于0',
      'number.max': '每页数量不能超过100'
    }),
  status: Joi.string()
    .valid('pending', 'completed', 'failed', 'refunded')
    .optional()
    .messages({
      'any.only': '状态只能是pending、completed、failed或refunded'
    }),
  user_search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': '用户搜索关键词长度不能超过100个字符'
    }),
  order_search: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': '订单搜索关键词长度不能超过100个字符'
    })
});

module.exports = {
  updateUserStatusSchema,
  userUuidSchema,
  paginationSchema,
  createAiRoleSchema,
  updateAiRoleSchema,
  aiRoleIdSchema,
  orderQuerySchema,
  validate,
  validateParams,
  validateQuery
};
