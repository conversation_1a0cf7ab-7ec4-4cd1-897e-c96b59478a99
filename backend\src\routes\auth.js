const express = require('express');
const authController = require('../controllers/authController');
const { validate, registerSchema, loginSchema } = require('../validators/auth');
const { authenticateToken } = require('../middleware/auth');
const { loginRateLimit, registerRateLimit } = require('../middleware/security');

const router = express.Router();

/**
 * @route POST /api/v1/auth/guest
 * @desc 创建游客账户
 * @access Public
 */
router.post('/guest', authController.createGuest);

/**
 * @route POST /api/v1/auth/register
 * @desc 用户注册
 * @access Public
 */
router.post('/register', registerRateLimit(), validate(registerSchema), authController.register);

/**
 * @route POST /api/v1/auth/login
 * @desc 用户登录
 * @access Public
 */
router.post('/login', loginRateLimit(), validate(loginSchema), authController.login);

/**
 * @route GET /api/v1/auth/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/me', authenticateToken, authController.getCurrentUser);

module.exports = router;
