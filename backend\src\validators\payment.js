const Joi = require('joi');

/**
 * 创建订单验证
 */
const createOrderSchema = Joi.object({
  plan_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '套餐ID必须是数字',
      'number.integer': '套餐ID必须是整数',
      'number.positive': '套餐ID必须是正数',
      'any.required': '套餐ID是必填项'
    })
});

/**
 * 支付通知验证（模拟）
 */
const paymentNotifySchema = Joi.object({
  order_uuid: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': '订单ID格式无效',
      'any.required': '订单ID是必填项'
    }),
  status: Joi.string()
    .valid('success', 'failed')
    .required()
    .messages({
      'any.only': '支付状态必须是success或failed',
      'any.required': '支付状态是必填项'
    }),
  transaction_id: Joi.string()
    .max(255)
    .optional()
    .messages({
      'string.max': '交易号长度不能超过255个字符'
    }),
  gateway: Joi.string()
    .max(50)
    .optional()
    .messages({
      'string.max': '支付网关名称长度不能超过50个字符'
    })
});

/**
 * 验证中间件工厂函数
 */
function validate(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

module.exports = {
  createOrderSchema,
  paymentNotifySchema,
  validate
};
