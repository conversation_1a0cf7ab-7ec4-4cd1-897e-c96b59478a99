const request = require('supertest');
const app = require('../src/app');

describe('AI角色模块测试', () => {
  
  describe('GET /api/v1/ai-roles', () => {
    test('应该成功获取AI角色列表', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles')
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // 检查返回的数据结构
      if (response.body.data.length > 0) {
        const role = response.body.data[0];
        expect(role).toHaveProperty('id');
        expect(role).toHaveProperty('name');
        expect(role).toHaveProperty('avatar_url');
        expect(role).toHaveProperty('description');
        
        // 不应该包含敏感信息
        expect(role).not.toHaveProperty('system_prompt');
        expect(role).not.toHaveProperty('details');
      }
    });

    test('应该返回按ID升序排列的角色列表', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles')
        .expect(200);

      const roles = response.body.data;
      if (roles.length > 1) {
        for (let i = 1; i < roles.length; i++) {
          expect(roles[i].id).toBeGreaterThan(roles[i-1].id);
        }
      }
    });
  });

  describe('GET /api/v1/ai-roles/:id', () => {
    let validRoleId;

    beforeAll(async () => {
      // 先获取一个有效的角色ID
      const response = await request(app).get('/api/v1/ai-roles');
      if (response.body.data.length > 0) {
        validRoleId = response.body.data[0].id;
      }
    });

    test('应该成功获取指定AI角色详情', async () => {
      if (!validRoleId) {
        console.log('跳过测试：没有可用的AI角色');
        return;
      }

      const response = await request(app)
        .get(`/api/v1/ai-roles/${validRoleId}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(response.body.data).toHaveProperty('id', validRoleId);
      expect(response.body.data).toHaveProperty('name');
      expect(response.body.data).toHaveProperty('avatar_url');
      expect(response.body.data).toHaveProperty('description');
      expect(response.body.data).toHaveProperty('details');
      expect(response.body.data).toHaveProperty('system_prompt');
      expect(response.body.data).toHaveProperty('created_at');
      expect(response.body.data).toHaveProperty('updated_at');
    });

    test('应该拒绝不存在的AI角色ID', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles/99999')
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('AI Role not found');
      expect(response.body.data).toBe(null);
    });

    test('应该拒绝无效的AI角色ID格式', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles/invalid')
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝负数ID', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles/-1')
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝零ID', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles/0')
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝小数ID', async () => {
      const response = await request(app)
        .get('/api/v1/ai-roles/1.5')
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('数据一致性测试', () => {
    test('角色列表和详情数据应该一致', async () => {
      // 获取角色列表
      const listResponse = await request(app)
        .get('/api/v1/ai-roles')
        .expect(200);

      const roles = listResponse.body.data;
      
      if (roles.length === 0) {
        console.log('跳过测试：没有可用的AI角色');
        return;
      }

      // 检查第一个角色的详情
      const roleId = roles[0].id;
      const detailResponse = await request(app)
        .get(`/api/v1/ai-roles/${roleId}`)
        .expect(200);

      const roleDetail = detailResponse.body.data;

      // 验证基本信息一致性
      expect(roleDetail.id).toBe(roles[0].id);
      expect(roleDetail.name).toBe(roles[0].name);
      expect(roleDetail.avatar_url).toBe(roles[0].avatar_url);
      expect(roleDetail.description).toBe(roles[0].description);
    });
  });
});
