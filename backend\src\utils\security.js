const validator = require('validator');

/**
 * 清理HTML内容，防止XSS攻击
 * @param {string} html - 要清理的HTML内容
 * @param {Object} options - 清理选项
 * @returns {string} 清理后的HTML
 */
function sanitizeHtml(html, options = {}) {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // 简单的HTML标签清理，移除所有HTML标签
  let cleaned = html.replace(/<[^>]*>/g, '');

  // 解码HTML实体
  cleaned = cleaned
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'");

  return cleaned;
}

/**
 * 清理文本内容，移除潜在的恶意字符
 * @param {string} text - 要清理的文本
 * @param {Object} options - 清理选项
 * @returns {string} 清理后的文本
 */
function sanitizeText(text, options = {}) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  const {
    maxLength = 10000,
    allowNewlines = true,
    allowSpecialChars = true
  } = options;

  // 基本清理
  let cleaned = text.trim();

  // 长度限制
  if (cleaned.length > maxLength) {
    cleaned = cleaned.substring(0, maxLength);
  }

  // 移除控制字符（除了换行符和制表符）
  if (!allowNewlines) {
    cleaned = cleaned.replace(/[\r\n\t]/g, ' ');
  } else {
    cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  }

  // 移除特殊字符（如果不允许）
  if (!allowSpecialChars) {
    cleaned = cleaned.replace(/[<>'"&]/g, '');
  }

  // 规范化空白字符
  cleaned = cleaned.replace(/\s+/g, ' ');

  return cleaned;
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  return validator.isEmail(email) && email.length <= 254;
}

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {Object} 验证结果
 */
function validatePassword(password) {
  if (!password || typeof password !== 'string') {
    return {
      valid: false,
      errors: ['密码不能为空']
    };
  }

  const errors = [];
  
  // 长度检查
  if (password.length < 8) {
    errors.push('密码长度至少8位');
  }
  
  if (password.length > 128) {
    errors.push('密码长度不能超过128位');
  }

  // 复杂度检查
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('密码必须包含数字');
  }

  // 常见弱密码检查
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', '123123'
  ];
  
  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('密码过于简单，请使用更复杂的密码');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 验证UUID格式
 * @param {string} uuid - UUID字符串
 * @returns {boolean} 是否为有效UUID
 */
function validateUuid(uuid) {
  if (!uuid || typeof uuid !== 'string') {
    return false;
  }
  
  return validator.isUUID(uuid);
}

/**
 * 验证数字范围
 * @param {any} value - 要验证的值
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {boolean} 是否在有效范围内
 */
function validateNumberRange(value, min = 0, max = Number.MAX_SAFE_INTEGER) {
  const num = Number(value);
  return !isNaN(num) && num >= min && num <= max && Number.isInteger(num);
}

/**
 * 防止SQL注入的参数清理
 * @param {string} input - 输入字符串
 * @returns {string} 清理后的字符串
 */
function sanitizeSqlInput(input) {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // 移除SQL关键字和特殊字符
  return input
    .replace(/['";\\]/g, '') // 移除引号和反斜杠
    .replace(/--/g, '') // 移除SQL注释
    .replace(/\/\*/g, '') // 移除多行注释开始
    .replace(/\*\//g, '') // 移除多行注释结束
    .replace(/\bUNION\b/gi, '') // 移除UNION关键字
    .replace(/\bSELECT\b/gi, '') // 移除SELECT关键字
    .replace(/\bINSERT\b/gi, '') // 移除INSERT关键字
    .replace(/\bUPDATE\b/gi, '') // 移除UPDATE关键字
    .replace(/\bDELETE\b/gi, '') // 移除DELETE关键字
    .replace(/\bDROP\b/gi, '') // 移除DROP关键字
    .trim();
}

/**
 * 生成安全的随机字符串
 * @param {number} length - 字符串长度
 * @param {string} charset - 字符集
 * @returns {string} 随机字符串
 */
function generateSecureRandomString(length = 32, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  const crypto = require('crypto');
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    result += charset[randomIndex];
  }
  
  return result;
}

/**
 * 验证请求频率限制
 * @param {string} identifier - 标识符（如IP地址或用户ID）
 * @param {number} maxRequests - 最大请求数
 * @param {number} windowMs - 时间窗口（毫秒）
 * @returns {Object} 验证结果
 */
const rateLimitStore = new Map();

function checkRateLimit(identifier, maxRequests = 100, windowMs = 60000) {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // 获取或创建请求记录
  if (!rateLimitStore.has(identifier)) {
    rateLimitStore.set(identifier, []);
  }
  
  const requests = rateLimitStore.get(identifier);
  
  // 清理过期的请求记录
  const validRequests = requests.filter(timestamp => timestamp > windowStart);
  rateLimitStore.set(identifier, validRequests);
  
  // 检查是否超过限制
  if (validRequests.length >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: Math.min(...validRequests) + windowMs
    };
  }
  
  // 添加当前请求
  validRequests.push(now);
  rateLimitStore.set(identifier, validRequests);
  
  return {
    allowed: true,
    remaining: maxRequests - validRequests.length,
    resetTime: now + windowMs
  };
}

/**
 * 清理定时任务，移除过期的频率限制记录
 */
function cleanupRateLimitStore() {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  for (const [identifier, requests] of rateLimitStore.entries()) {
    const validRequests = requests.filter(timestamp => timestamp > now - oneHour);
    if (validRequests.length === 0) {
      rateLimitStore.delete(identifier);
    } else {
      rateLimitStore.set(identifier, validRequests);
    }
  }
}

// 每小时清理一次频率限制存储
setInterval(cleanupRateLimitStore, 60 * 60 * 1000);

module.exports = {
  sanitizeHtml,
  sanitizeText,
  validateEmail,
  validatePassword,
  validateUuid,
  validateNumberRange,
  sanitizeSqlInput,
  generateSecureRandomString,
  checkRateLimit
};
