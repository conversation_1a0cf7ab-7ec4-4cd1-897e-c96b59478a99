# AI伙伴聊天应用 - 后端API

## 项目概述

AI伙伴聊天应用是一个功能完整的智能聊天平台，支持多角色AI对话、用户管理、套餐订阅等功能。本项目采用Node.js + Express + MySQL技术栈，具备企业级的安全性、性能和可靠性。

## 🚀 核心功能

- **智能聊天**: 支持多种AI角色的对话交互
- **用户系统**: 游客模式、注册用户、管理员权限
- **套餐管理**: 订阅套餐、一次性消息包、每日免费额度
- **支付系统**: 订单管理、支付回调、用户订阅
- **安全防护**: 输入验证、频率限制、JWT认证
- **性能优化**: 分页查询、缓存策略、异步处理

## 📚 文档目录

### 核心API文档
- [**用户认证与管理 (模块一)**](./MODULE_1_API_DOCUMENTATION.md) - 用户注册、登录、游客模式
- [**AI角色管理 (模块二)**](./MODULE_2_API_DOCUMENTATION.md) - AI角色的CRUD操作
- [**聊天功能 (模块三)**](./MODULE_3_API_DOCUMENTATION.md) - 会话管理、消息发送、历史记录
- [**用户中心 (模块四)**](./MODULE_4_API_DOCUMENTATION.md) - 个人信息、使用统计、设置
- [**套餐与支付 (模块五)**](./MODULE_5_API_DOCUMENTATION.md) - 套餐购买、支付处理、订单管理

### 扩展功能文档
- [**管理员功能 (模块六)**](./MODULE_6_EXTENDED_API_DOCUMENTATION.md) - 用户管理、AI角色管理、订单管理、套餐管理
- [**聊天删除功能**](./CHAT_DELETION_API_DOCUMENTATION.md) - 删除会话、清空消息、批量删除
- [**套餐管理功能**](./PLAN_MANAGEMENT_API_DOCUMENTATION.md) - 套餐创建、更新、禁用

### 最佳实践文档
- [**API使用建议与最佳实践**](./API_BEST_PRACTICES_DOCUMENTATION.md) - 游客转正、每日额度、安全防护、性能优化、异步处理

## 🛠️ 技术栈

- **运行环境**: Node.js 18+
- **Web框架**: Express.js
- **数据库**: MySQL 8.0
- **缓存**: Redis (可选)
- **认证**: JWT (JSON Web Tokens)
- **密码加密**: bcrypt
- **输入验证**: Joi
- **测试框架**: Jest + Supertest
- **API文档**: 内置文档系统

## 📦 安装和运行

### 环境要求
- Node.js 18.0+
- MySQL 8.0+
- npm 或 yarn

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-companion-backend
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **数据库初始化**
```bash
# 数据库会在首次启动时自动创建表结构
npm start
```

5. **运行测试**
```bash
npm test
```

6. **启动开发服务器**
```bash
npm run dev
```

## 🔧 环境变量配置

```bash
# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=ai_companion

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
JWT_GUEST_EXPIRES_IN=1d

# 服务器配置
PORT=3000
NODE_ENV=development

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 每日额度配置
GUEST_DAILY_LIMIT=10
USER_DAILY_LIMIT=50

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🧪 测试覆盖

项目包含完整的测试套件，覆盖所有核心功能：

### 测试统计
- **用户认证**: 10个测试用例 ✅
- **AI角色管理**: 10个测试用例 ✅
- **聊天功能**: 20个测试用例 ✅
- **聊天删除**: 7个测试用例 ✅
- **管理员功能**: 15个测试用例 ✅
- **套餐管理**: 12个测试用例 ✅
- **最佳实践**: 15个测试用例 ✅

**总计: 89个测试用例全部通过** 🎉

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定模块测试
npm test -- test/auth.test.js
npm test -- test/chat.test.js
npm test -- test/admin.test.js

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 🚀 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t ai-companion-api .

# 使用docker-compose部署
docker-compose up -d
```

### 生产环境配置
- 使用PM2进行进程管理
- 配置Nginx反向代理
- 设置SSL证书
- 配置Redis缓存
- 设置定时任务

详细部署指南请参考 [API最佳实践文档](./API_BEST_PRACTICES_DOCUMENTATION.md)

## 📊 API接口概览

### 认证相关
- `POST /api/v1/auth/guest` - 创建游客账户
- `POST /api/v1/auth/register` - 用户注册（支持游客转正）
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息

### 聊天功能
- `POST /api/v1/chat/sessions` - 创建聊天会话
- `GET /api/v1/chat/sessions` - 获取用户会话列表
- `POST /api/v1/chat/sessions/:uuid/messages` - 发送消息
- `GET /api/v1/chat/sessions/:uuid/messages` - 获取消息历史
- `DELETE /api/v1/chat/sessions/:uuid` - 删除会话

### 管理员功能
- `GET /api/v1/admin/users` - 用户管理
- `GET /api/v1/admin/ai-roles` - AI角色管理
- `GET /api/v1/admin/orders` - 订单管理
- `POST /api/v1/admin/plans` - 套餐管理

### 其他功能
- `GET /api/v1/ai-roles` - 获取AI角色列表
- `GET /api/v1/plans` - 获取套餐列表
- `POST /api/v1/orders` - 创建订单
- `GET /api/v1/health` - 健康检查

## 🔒 安全特性

- **JWT认证**: 安全的用户身份验证
- **密码加密**: bcrypt加盐哈希
- **输入验证**: 严格的参数验证和清理
- **频率限制**: 防止API滥用和暴力攻击
- **SQL注入防护**: 参数化查询
- **XSS防护**: 输入内容清理
- **CORS配置**: 跨域请求控制
- **安全头**: 完整的HTTP安全头设置

## 🎯 性能优化

- **分页查询**: 避免大数据量查询
- **数据库索引**: 优化查询性能
- **缓存策略**: Redis缓存热点数据
- **异步处理**: 消息队列处理耗时操作
- **连接池**: 数据库连接复用
- **压缩**: Gzip响应压缩

## 📈 监控和日志

- **健康检查**: `/api/v1/health` 端点
- **性能指标**: Prometheus集成
- **结构化日志**: Winston日志系统
- **错误追踪**: 完整的错误堆栈记录
- **用户行为**: 操作日志记录

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件
- 查看文档

---

**AI伙伴聊天应用** - 让智能对话更简单 🤖✨
