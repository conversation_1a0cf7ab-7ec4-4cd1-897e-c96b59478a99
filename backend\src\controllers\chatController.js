const chatService = require('../services/chatService');

/**
 * 创建新的聊天会话
 */
async function createSession(req, res, next) {
  try {
    const { ai_role_id } = req.body;
    const userId = req.user.id;

    const session = await chatService.createChatSession(userId, ai_role_id);
    
    res.success(session, 'Chat session created successfully', 201);
  } catch (error) {
    if (error.message === 'AI_ROLE_NOT_FOUND') {
      return res.error('AI角色不存在或已禁用', 40401, 404);
    }
    next(error);
  }
}

/**
 * 发送消息
 */
async function sendMessage(req, res, next) {
  try {
    const { session_uuid } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    const aiMessage = await chatService.sendMessage(session_uuid, userId, content);
    
    res.success(aiMessage, 'Message sent successfully');
  } catch (error) {
    if (error.message === 'SESSION_NOT_FOUND') {
      return res.error('聊天会话不存在或无权访问', 40401, 404);
    }
    if (error.message.startsWith('AI_REPLY_FAILED:')) {
      return res.error('AI回复生成失败，请稍后重试', 50002, 500);
    }
    next(error);
  }
}

/**
 * 获取会话历史消息
 */
async function getMessages(req, res, next) {
  try {
    const { session_uuid } = req.params;
    const userId = req.user.id;

    const messages = await chatService.getSessionMessages(session_uuid, userId);
    
    res.success(messages, 'Messages retrieved successfully');
  } catch (error) {
    if (error.message === 'SESSION_NOT_FOUND') {
      return res.error('聊天会话不存在或无权访问', 40401, 404);
    }
    next(error);
  }
}

/**
 * 获取用户的所有聊天会话
 */
async function getSessions(req, res, next) {
  try {
    const userId = req.user.id;

    const sessions = await chatService.getUserSessions(userId);
    
    res.success(sessions, 'Sessions retrieved successfully');
  } catch (error) {
    next(error);
  }
}

module.exports = {
  createSession,
  sendMessage,
  getMessages,
  getSessions
};
