const chatService = require('../services/chatService');

/**
 * 创建新的聊天会话
 */
async function createSession(req, res, next) {
  try {
    const { ai_role_id } = req.body;
    const userId = req.user.id;

    const session = await chatService.createChatSession(userId, ai_role_id);
    
    res.success(session, 'Chat session created successfully', 201);
  } catch (error) {
    if (error.message === 'AI_ROLE_NOT_FOUND') {
      return res.error('AI角色不存在或已禁用', 40401, 404);
    }
    next(error);
  }
}

/**
 * 发送消息
 */
async function sendMessage(req, res, next) {
  try {
    const { session_uuid } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    const aiMessage = await chatService.sendMessage(session_uuid, userId, content);
    
    res.success(aiMessage, 'Message sent successfully');
  } catch (error) {
    if (error.message === 'SESSION_NOT_FOUND') {
      return res.error('聊天会话不存在或无权访问', 40401, 404);
    }
    if (error.message.startsWith('AI_REPLY_FAILED:')) {
      return res.error('AI回复生成失败，请稍后重试', 50002, 500);
    }
    next(error);
  }
}

/**
 * 获取会话历史消息
 */
async function getMessages(req, res, next) {
  try {
    const { session_uuid } = req.params;
    const userId = req.user.id;

    const messages = await chatService.getSessionMessages(session_uuid, userId);
    
    res.success(messages, 'Messages retrieved successfully');
  } catch (error) {
    if (error.message === 'SESSION_NOT_FOUND') {
      return res.error('聊天会话不存在或无权访问', 40401, 404);
    }
    next(error);
  }
}

/**
 * 获取用户的所有聊天会话
 */
async function getSessions(req, res, next) {
  try {
    const userId = req.user.id;

    const sessions = await chatService.getUserSessions(userId);
    
    res.success(sessions, 'Sessions retrieved successfully');
  } catch (error) {
    next(error);
  }
}

/**
 * 发送消息（流式响应）
 */
async function sendMessageStream(req, res, next) {
  try {
    const { session_uuid } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 发送初始连接确认
    res.write('data: {"type":"connected"}\n\n');

    await chatService.sendMessageStream(
      session_uuid,
      userId,
      content,
      // onChunk回调：发送流式数据块
      (chunk, fullContent) => {
        const data = {
          type: 'chunk',
          content: chunk,
          fullContent: fullContent
        };
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      },
      // onComplete回调：发送完成信息
      (aiMessage) => {
        const data = {
          type: 'complete',
          message: aiMessage
        };
        res.write(`data: ${JSON.stringify(data)}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
      },
      // onError回调：发送错误信息
      (error) => {
        let errorMessage = 'AI回复生成失败，请稍后重试';
        let errorCode = 50002;

        if (error.message === 'SESSION_NOT_FOUND') {
          errorMessage = '聊天会话不存在或无权访问';
          errorCode = 40401;
        }

        const data = {
          type: 'error',
          code: errorCode,
          message: errorMessage
        };
        res.write(`data: ${JSON.stringify(data)}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
      }
    );

  } catch (error) {
    // 如果在设置SSE之前出错，使用普通错误响应
    if (!res.headersSent) {
      if (error.message === 'SESSION_NOT_FOUND') {
        return res.error('聊天会话不存在或无权访问', 40401, 404);
      }
      next(error);
    } else {
      // 如果已经开始SSE响应，发送错误事件
      const data = {
        type: 'error',
        code: 50001,
        message: '服务器内部错误'
      };
      res.write(`data: ${JSON.stringify(data)}\n\n`);
      res.write('data: [DONE]\n\n');
      res.end();
    }
  }
}

module.exports = {
  createSession,
  sendMessage,
  sendMessageStream,
  getMessages,
  getSessions
};
