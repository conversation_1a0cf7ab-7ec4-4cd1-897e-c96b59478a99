const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');
const { getDailyQuotaInfo, consumeQuota } = require('../src/utils/dailyQuota');
const { hasActiveSubscription } = require('../src/services/paymentService');

describe('调试额度系统', () => {
  let normalUserId;

  beforeAll(async () => {
    // 创建普通用户
    const testEmail = `debug_quota_${Date.now()}@example.com`;
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: 'test123456'
      });

    if (userResponse.body.code === 201) {
      const normalUsers = await query('SELECT id FROM users WHERE email = ?', [testEmail]);
      normalUserId = normalUsers[0].id;
      
      // 确保没有订阅记录
      await query('DELETE FROM user_subscriptions WHERE user_id = ?', [normalUserId]);
      
      // 重置用户状态
      await query('UPDATE users SET daily_message_count = 0, message_credits = 0 WHERE id = ?', [normalUserId]);
    }
  });

  test('调试普通用户额度消费', async () => {
    if (!normalUserId) {
      console.log('跳过测试：用户创建失败');
      return;
    }

    console.log('=== 开始调试普通用户额度消费 ===');
    
    // 1. 检查初始状态
    const initialVipStatus = await hasActiveSubscription(normalUserId);
    const initialQuota = await getDailyQuotaInfo(normalUserId);
    
    console.log('1. 初始VIP状态:', initialVipStatus);
    console.log('1. 初始额度信息:', initialQuota);
    
    // 2. 检查数据库中的订阅记录
    const subscriptions = await query(
      'SELECT * FROM user_subscriptions WHERE user_id = ?',
      [normalUserId]
    );
    console.log('2. 数据库订阅记录:', subscriptions);
    
    // 3. 消费49条消息
    console.log('3. 开始消费49条消息...');
    for (let i = 0; i < 49; i++) {
      const result = await consumeQuota(normalUserId, 1);
      if (!result.success) {
        console.log(`第${i+1}条消费失败:`, result);
        break;
      }
    }
    
    // 4. 检查消费49条后的状态
    const afterVipStatus = await hasActiveSubscription(normalUserId);
    const afterQuota = await getDailyQuotaInfo(normalUserId);
    
    console.log('4. 消费49条后VIP状态:', afterVipStatus);
    console.log('4. 消费49条后额度信息:', afterQuota);
    
    // 5. 尝试消费第50条
    console.log('5. 尝试消费第50条...');
    const result50 = await consumeQuota(normalUserId, 1);
    console.log('5. 第50条消费结果:', result50);
    
    // 6. 检查消费50条后的状态
    const final50VipStatus = await hasActiveSubscription(normalUserId);
    const final50Quota = await getDailyQuotaInfo(normalUserId);
    
    console.log('6. 消费50条后VIP状态:', final50VipStatus);
    console.log('6. 消费50条后额度信息:', final50Quota);
    
    // 7. 尝试消费第51条（应该失败）
    console.log('7. 尝试消费第51条（应该失败）...');
    const result51 = await consumeQuota(normalUserId, 1);
    console.log('7. 第51条消费结果:', result51);
    
    // 8. 最终检查
    const finalVipStatus = await hasActiveSubscription(normalUserId);
    const finalQuota = await getDailyQuotaInfo(normalUserId);
    
    console.log('8. 最终VIP状态:', finalVipStatus);
    console.log('8. 最终额度信息:', finalQuota);
    
    console.log('=== 调试结束 ===');
    
    // 验证第51条应该失败
    expect(result51.success).toBe(false);
  });

  afterAll(async () => {
    // 清理测试数据
    if (normalUserId) {
      await query('DELETE FROM user_subscriptions WHERE user_id = ?', [normalUserId]);
      await query('DELETE FROM orders WHERE user_id = ?', [normalUserId]);
      await query('DELETE FROM chat_messages WHERE session_id IN (SELECT id FROM chat_sessions WHERE user_id = ?)', [normalUserId]);
      await query('DELETE FROM chat_sessions WHERE user_id = ?', [normalUserId]);
      await query('DELETE FROM users WHERE id = ?', [normalUserId]);
    }
  });
});
