# AI伙伴聊天应用 - 模块六扩展API文档

## 概述

本文档描述了AI伙伴聊天应用**模块六扩展功能：AI角色管理、订单管理和套餐管理**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1/admin`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token（管理员权限）

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40001,
  "message": "角色名称已存在",
  "data": null
}
```

## 权限说明

所有管理员接口都需要：
1. **有效的JWT Token**: 用户必须已登录
2. **管理员角色**: 用户的role字段必须为"admin"

---

## AI角色管理接口

### 1. 创建AI角色

**接口描述**: 创建新的AI角色

- **URL**: `POST /ai-roles`
- **认证**: 需要管理员权限

**请求参数**:
```json
{
  "name": "智能助手",
  "avatar_url": "https://example.com/avatar.jpg",
  "description": "一个友好的AI助手",
  "details": "详细的角色描述信息",
  "system_prompt": "你是一个友好的AI助手，请帮助用户解决问题。"
}
```

**参数说明**:
- `name`: 角色名称（必填，1-100字符，不能重复）
- `avatar_url`: 头像URL（可选，有效的URL格式）
- `description`: 角色简介（可选，最多500字符）
- `details`: 详细描述（可选）
- `system_prompt`: 系统提示词（必填，定义AI行为）

**响应示例**:
```json
{
  "code": 201,
  "message": "AI role created successfully",
  "data": {
    "id": 5,
    "name": "智能助手",
    "avatar_url": "https://example.com/avatar.jpg",
    "description": "一个友好的AI助手",
    "details": "详细的角色描述信息",
    "system_prompt": "你是一个友好的AI助手，请帮助用户解决问题。",
    "is_active": true
  }
}
```

**错误响应**:
- `400 Bad Request`: 角色名称已存在、参数验证失败
- `403 Forbidden`: 需要管理员权限

### 2. 更新AI角色

**接口描述**: 更新指定AI角色的信息

- **URL**: `PUT /ai-roles/{id}`
- **认证**: 需要管理员权限

**路径参数**:
- `id`: AI角色ID（必须是正整数）

**请求参数**:
```json
{
  "name": "更新后的角色名称",
  "description": "更新后的描述",
  "system_prompt": "更新后的系统提示词"
}
```

**参数说明**:
- 所有字段都是可选的，只更新提供的字段
- 字段验证规则与创建接口相同

**响应示例**:
```json
{
  "code": 200,
  "message": "AI role updated successfully",
  "data": {
    "id": 5,
    "name": "更新后的角色名称",
    "avatar_url": "https://example.com/avatar.jpg",
    "description": "更新后的描述",
    "details": "详细的角色描述信息",
    "system_prompt": "更新后的系统提示词",
    "is_active": true,
    "created_at": "2025-07-22T10:00:00.000Z",
    "updated_at": "2025-07-22T15:30:00.000Z"
  }
}
```

**错误响应**:
- `400 Bad Request`: 角色名称已存在、没有提供更新数据、参数验证失败
- `404 Not Found`: AI角色不存在

### 3. 删除AI角色

**接口描述**: 删除AI角色（软删除，设置is_active为false）

- **URL**: `DELETE /ai-roles/{id}`
- **认证**: 需要管理员权限

**路径参数**:
- `id`: AI角色ID（必须是正整数）

**响应示例**:
```json
{
  "code": 200,
  "message": "AI role deleted successfully",
  "data": null
}
```

**业务规则**:
- 软删除：将is_active设置为false，不物理删除数据
- 如果角色正在被聊天会话使用，则无法删除
- 已删除的角色无法再次删除

**错误响应**:
- `400 Bad Request`: AI角色已被删除、AI角色正在使用中
- `404 Not Found`: AI角色不存在

---

## 订单管理接口

### 1. 获取订单列表

**接口描述**: 获取系统所有订单列表，支持分页和多种筛选条件

- **URL**: `GET /orders`
- **认证**: 需要管理员权限

**查询参数**:
| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码，从1开始 |
| limit | number | 否 | 20 | 每页数量，最大100 |
| status | string | 否 | - | 订单状态筛选 |
| user_search | string | 否 | - | 用户搜索（邮箱或UUID） |
| order_search | string | 否 | - | 订单搜索（订单UUID） |

**状态值说明**:
- `pending`: 待支付
- `completed`: 已完成
- `failed`: 支付失败
- `refunded`: 已退款

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "orders": [
      {
        "uuid": "e5f6a7b8-c9d0-1234-5678-90abcdef1234",
        "amount": "20.00",
        "status": "completed",
        "payment_gateway": "mock_payment",
        "gateway_transaction_id": "mock_txn_1234567890",
        "created_at": "2025-07-22T10:00:00.000Z",
        "updated_at": "2025-07-22T10:05:00.000Z",
        "user_uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
        "user_email": "<EMAIL>",
        "is_guest": false,
        "plan_name": "月度会员",
        "plan_type": "subscription"
      },
      {
        "uuid": "f6a7b8c9-d0e1-2345-6789-0abcdef12345",
        "amount": "50.00",
        "status": "pending",
        "payment_gateway": "mock_payment",
        "gateway_transaction_id": null,
        "created_at": "2025-07-22T09:30:00.000Z",
        "updated_at": "2025-07-22T09:30:00.000Z",
        "user_uuid": "c3d4e5f6-a7b8-9012-3456-7890abcdef12",
        "user_email": null,
        "is_guest": true,
        "plan_name": "消息加油包",
        "plan_type": "one_time"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 125,
      "pages": 7
    }
  }
}
```

**字段说明**:
- `uuid`: 订单唯一标识符
- `amount`: 订单金额
- `status`: 订单状态
- `payment_gateway`: 支付网关
- `gateway_transaction_id`: 支付网关交易号
- `user_uuid`: 用户UUID
- `user_email`: 用户邮箱（游客为null）
- `is_guest`: 是否为游客用户
- `plan_name`: 套餐名称
- `plan_type`: 套餐类型

**特点**:
- 按创建时间倒序排列（最新的在前）
- 支持多种筛选条件组合使用
- 包含用户和套餐的关联信息
- 完整的分页信息

**错误响应**:
- `400 Bad Request`: 参数验证失败
- `403 Forbidden`: 需要管理员权限

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 创建AI角色
async function createAiRole(token, roleData) {
  try {
    const response = await fetch('/api/v1/admin/ai-roles', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(roleData)
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('AI角色创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建AI角色失败:', error);
    throw error;
  }
}

// 2. 更新AI角色
async function updateAiRole(token, roleId, updateData) {
  try {
    const response = await fetch(`/api/v1/admin/ai-roles/${roleId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('AI角色更新成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新AI角色失败:', error);
    throw error;
  }
}

// 3. 删除AI角色
async function deleteAiRole(token, roleId) {
  try {
    const response = await fetch(`/api/v1/admin/ai-roles/${roleId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('AI角色删除成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('删除AI角色失败:', error);
    throw error;
  }
}

// 4. 获取订单列表
async function getOrderList(token, filters = {}) {
  try {
    const params = new URLSearchParams();

    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });

    const response = await fetch(`/api/v1/admin/orders?${params}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('订单列表获取成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw error;
  }
}

// 5. 创建套餐
async function createPlan(token, planData) {
  try {
    const response = await fetch('/api/v1/admin/plans', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(planData)
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('套餐创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建套餐失败:', error);
    throw error;
  }
}

// 6. 更新套餐
async function updatePlan(token, planId, updateData) {
  try {
    const response = await fetch(`/api/v1/admin/plans/${planId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('套餐更新成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新套餐失败:', error);
    throw error;
  }
}

// 7. 禁用套餐
async function disablePlan(token, planId) {
  try {
    const response = await fetch(`/api/v1/admin/plans/${planId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('套餐禁用成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('禁用套餐失败:', error);
    throw error;
  }
}

// 使用示例
async function example() {
  const adminToken = 'your-admin-token';

  // 创建AI角色
  const newRole = await createAiRole(adminToken, {
    name: '专业顾问',
    avatar_url: 'https://example.com/avatar.jpg',
    description: '专业的咨询顾问',
    system_prompt: '你是一个专业的咨询顾问，请为用户提供专业建议。'
  });

  // 更新AI角色
  const updatedRole = await updateAiRole(adminToken, newRole.id, {
    description: '更新后的描述'
  });

  // 获取订单列表
  const orders = await getOrderList(adminToken, {
    page: 1,
    limit: 10,
    status: 'completed'
  });

  // 删除AI角色
  await deleteAiRole(adminToken, newRole.id);
}
```

### Vue.js 管理组件示例

```vue
<template>
  <div class="admin-extended">
    <!-- AI角色管理 -->
    <div class="ai-roles-section">
      <h2>AI角色管理</h2>
      
      <button @click="showCreateRoleModal = true" class="btn-create">
        创建新角色
      </button>

      <div class="roles-list">
        <div v-for="role in aiRoles" :key="role.id" class="role-item">
          <div class="role-info">
            <img v-if="role.avatar_url" :src="role.avatar_url" class="role-avatar" />
            <div>
              <h4>{{ role.name }}</h4>
              <p>{{ role.description }}</p>
              <small>创建时间: {{ formatTime(role.created_at) }}</small>
            </div>
          </div>
          <div class="role-actions">
            <button @click="editRole(role)" class="btn-edit">编辑</button>
            <button @click="deleteRole(role)" class="btn-delete">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单管理 -->
    <div class="orders-section">
      <h2>订单管理</h2>
      
      <!-- 筛选条件 -->
      <div class="order-filters">
        <select v-model="orderFilters.status" @change="loadOrders">
          <option value="">全部状态</option>
          <option value="pending">待支付</option>
          <option value="completed">已完成</option>
          <option value="failed">支付失败</option>
          <option value="refunded">已退款</option>
        </select>
        
        <input 
          v-model="orderFilters.user_search" 
          @input="searchOrders"
          placeholder="搜索用户（邮箱或UUID）"
        />
        
        <input 
          v-model="orderFilters.order_search" 
          @input="searchOrders"
          placeholder="搜索订单（订单号）"
        />
      </div>

      <!-- 订单列表 -->
      <div class="orders-list">
        <table>
          <thead>
            <tr>
              <th>订单号</th>
              <th>用户</th>
              <th>套餐</th>
              <th>金额</th>
              <th>状态</th>
              <th>支付方式</th>
              <th>创建时间</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in orders" :key="order.uuid">
              <td>{{ order.uuid.substring(0, 8) }}...</td>
              <td>
                {{ order.user_email || '游客用户' }}
                <br>
                <small>{{ order.user_uuid.substring(0, 8) }}...</small>
              </td>
              <td>{{ order.plan_name }}</td>
              <td>¥{{ order.amount }}</td>
              <td>
                <span :class="['status', order.status]">
                  {{ getStatusText(order.status) }}
                </span>
              </td>
              <td>{{ order.payment_gateway || '-' }}</td>
              <td>{{ formatTime(order.created_at) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button 
          @click="changeOrderPage(orderPage - 1)"
          :disabled="orderPage <= 1"
        >
          上一页
        </button>
        <span>第 {{ orderPage }} 页，共 {{ orderTotalPages }} 页</span>
        <button 
          @click="changeOrderPage(orderPage + 1)"
          :disabled="orderPage >= orderTotalPages"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑角色弹窗 -->
    <div v-if="showCreateRoleModal || editingRole" class="role-modal">
      <div class="modal-content">
        <h3>{{ editingRole ? '编辑角色' : '创建角色' }}</h3>
        <form @submit.prevent="saveRole">
          <div class="form-group">
            <label>角色名称 *</label>
            <input v-model="roleForm.name" required />
          </div>
          <div class="form-group">
            <label>头像URL</label>
            <input v-model="roleForm.avatar_url" type="url" />
          </div>
          <div class="form-group">
            <label>角色简介</label>
            <textarea v-model="roleForm.description" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label>详细描述</label>
            <textarea v-model="roleForm.details" rows="5"></textarea>
          </div>
          <div class="form-group">
            <label>系统提示词 *</label>
            <textarea v-model="roleForm.system_prompt" rows="5" required></textarea>
          </div>
          <div class="form-actions">
            <button type="submit" :disabled="saving">
              {{ saving ? '保存中...' : '保存' }}
            </button>
            <button type="button" @click="closeRoleModal">取消</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      aiRoles: [],
      orders: [],
      orderPage: 1,
      orderTotalPages: 1,
      orderFilters: {
        status: '',
        user_search: '',
        order_search: ''
      },
      showCreateRoleModal: false,
      editingRole: null,
      roleForm: {
        name: '',
        avatar_url: '',
        description: '',
        details: '',
        system_prompt: ''
      },
      saving: false,
      adminToken: localStorage.getItem('adminToken')
    }
  },

  async mounted() {
    await this.loadAiRoles();
    await this.loadOrders();
  },

  methods: {
    async loadAiRoles() {
      try {
        const response = await fetch('/api/v1/ai-roles');
        const result = await response.json();
        if (result.code === 200) {
          this.aiRoles = result.data;
        }
      } catch (error) {
        console.error('加载AI角色失败:', error);
      }
    },

    async loadOrders() {
      try {
        const params = new URLSearchParams({
          page: this.orderPage.toString(),
          limit: '20'
        });

        Object.keys(this.orderFilters).forEach(key => {
          if (this.orderFilters[key]) {
            params.append(key, this.orderFilters[key]);
          }
        });

        const response = await fetch(`/api/v1/admin/orders?${params}`, {
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });
        
        const result = await response.json();
        if (result.code === 200) {
          this.orders = result.data.orders;
          this.orderTotalPages = result.data.pagination.pages;
        }
      } catch (error) {
        console.error('加载订单列表失败:', error);
      }
    },

    async saveRole() {
      this.saving = true;
      try {
        const url = this.editingRole 
          ? `/api/v1/admin/ai-roles/${this.editingRole.id}`
          : '/api/v1/admin/ai-roles';
        
        const method = this.editingRole ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.adminToken}`
          },
          body: JSON.stringify(this.roleForm)
        });

        const result = await response.json();
        if (result.code === 200 || result.code === 201) {
          alert('保存成功');
          this.closeRoleModal();
          await this.loadAiRoles();
        } else {
          alert('保存失败: ' + result.message);
        }
      } catch (error) {
        console.error('保存角色失败:', error);
        alert('保存失败，请重试');
      } finally {
        this.saving = false;
      }
    },

    async deleteRole(role) {
      if (!confirm(`确定要删除角色"${role.name}"吗？`)) {
        return;
      }

      try {
        const response = await fetch(`/api/v1/admin/ai-roles/${role.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });

        const result = await response.json();
        if (result.code === 200) {
          alert('删除成功');
          await this.loadAiRoles();
        } else {
          alert('删除失败: ' + result.message);
        }
      } catch (error) {
        console.error('删除角色失败:', error);
        alert('删除失败，请重试');
      }
    },

    editRole(role) {
      this.editingRole = role;
      this.roleForm = { ...role };
    },

    closeRoleModal() {
      this.showCreateRoleModal = false;
      this.editingRole = null;
      this.roleForm = {
        name: '',
        avatar_url: '',
        description: '',
        details: '',
        system_prompt: ''
      };
    },

    searchOrders() {
      this.orderPage = 1;
      this.loadOrders();
    },

    changeOrderPage(page) {
      if (page >= 1 && page <= this.orderTotalPages) {
        this.orderPage = page;
        this.loadOrders();
      }
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    },

    getStatusText(status) {
      const statusMap = {
        pending: '待支付',
        completed: '已完成',
        failed: '支付失败',
        refunded: '已退款'
      };
      return statusMap[status] || status;
    }
  }
}
</script>

<style scoped>
.admin-extended {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.ai-roles-section, .orders-section {
  margin-bottom: 40px;
}

.btn-create {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
}

.role-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.role-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.order-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.order-filters select,
.order-filters input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th, td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.status.pending { color: #ffc107; }
.status.completed { color: #28a745; }
.status.failed { color: #dc3545; }
.status.refunded { color: #6c757d; }

.role-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  width: 90%;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}
</style>
```

---

## 数据库交互

### AI角色管理
- **创建**: 检查名称唯一性，插入新记录
- **更新**: 动态构建更新字段，支持部分更新
- **删除**: 软删除（is_active=false），检查使用状态

### 订单管理
- **查询**: 多表联查（orders、users、plans）
- **筛选**: 支持状态、用户、订单号多维度筛选
- **分页**: 高效的LIMIT/OFFSET分页

---

## 安全考虑

1. **权限验证**: 所有接口都需要管理员权限
2. **参数验证**: 严格的输入验证和格式检查
3. **业务规则**: 防止删除正在使用的AI角色
4. **软删除**: 保留数据完整性，避免级联删除问题
5. **SQL注入防护**: 使用参数化查询

---

## 注意事项

1. **AI角色删除**: 只有未被使用的角色才能删除
2. **名称唯一性**: AI角色名称在活跃角色中必须唯一
3. **软删除机制**: 删除的角色仍保留在数据库中
4. **订单数据**: 包含完整的用户和套餐关联信息
5. **分页性能**: 大数据量时建议使用索引优化

---

## 测试覆盖

模块六扩展功能包含完整的测试覆盖：

### AI角色管理测试
- ✅ 创建AI角色测试（成功、重复名称、缺少字段）
- ✅ 更新AI角色测试（成功、不存在、无效ID）
- ✅ 删除AI角色测试（成功、不存在、重复删除）
- ✅ 权限验证测试（管理员权限检查）

### 订单管理测试
- ✅ 订单列表获取测试（基本查询、分页）
- ✅ 订单筛选测试（状态、用户、订单号）
- ✅ 参数验证测试（分页参数、筛选条件）
- ✅ 权限验证测试（管理员权限检查）

### 套餐管理测试
- ✅ 创建套餐测试（订阅套餐、一次性套餐、业务规则验证）
- ✅ 更新套餐测试（成功、不存在、业务规则）
- ✅ 禁用套餐测试（成功、重复禁用、依赖检查）
- ✅ 参数验证测试（价格、类型、字段一致性）
- ✅ 权限验证测试（管理员权限检查）

所有27个测试用例均已通过验证，功能完整可用。
