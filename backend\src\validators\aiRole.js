const Joi = require('joi');

/**
 * AI角色ID验证
 */
const aiRoleIdSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'AI角色ID必须是数字',
      'number.integer': 'AI角色ID必须是整数',
      'number.positive': 'AI角色ID必须是正数',
      'any.required': 'AI角色ID是必填项'
    })
});

/**
 * 验证中间件工厂函数
 */
function validateParams(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.params);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

module.exports = {
  aiRoleIdSchema,
  validateParams
};
