# AI伙伴聊天应用 - 套餐管理API文档

## 概述

本文档描述了AI伙伴聊天应用**套餐管理 (Plan Management)**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1/admin`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token（管理员权限）

## 统一响应格式

### 成功响应
```json
{
  "code": 201,
  "message": "Plan created successfully",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40001,
  "message": "套餐名称已存在",
  "data": null
}
```

## 权限说明

所有套餐管理接口都需要：
1. **有效的JWT Token**: 用户必须已登录
2. **管理员角色**: 用户的role字段必须为"admin"

---

## 套餐管理接口

### 1. 创建套餐

**接口描述**: 创建新的套餐计划

- **URL**: `POST /plans`
- **认证**: 需要管理员权限

**请求参数**:

#### 订阅套餐示例
```json
{
  "name": "月度会员",
  "description": "无限畅聊，解锁所有高级功能",
  "price": 29.99,
  "plan_type": "subscription",
  "duration_days": 30
}
```

#### 一次性套餐示例
```json
{
  "name": "消息加油包",
  "description": "一次性购买500条消息额度",
  "price": 49.99,
  "plan_type": "one_time",
  "message_credits": 500
}
```

**参数说明**:
- `name`: 套餐名称（必填，1-100字符，不能重复）
- `description`: 套餐描述（可选，最多255字符）
- `price`: 价格（必填，0-99999.99，最多2位小数）
- `plan_type`: 套餐类型（必填）
  - `subscription`: 订阅类型（会员）
  - `one_time`: 一次性购买（消息包）
- `message_credits`: 消息点数（一次性套餐必填，1-999999）
- `duration_days`: 有效期天数（订阅套餐必填，1-3650天）

**业务规则**:
- 订阅套餐必须设置`duration_days`，不能设置`message_credits`
- 一次性套餐必须设置`message_credits`，不能设置`duration_days`
- 套餐名称在活跃套餐中必须唯一

**响应示例**:
```json
{
  "code": 201,
  "message": "Plan created successfully",
  "data": {
    "id": 5,
    "name": "月度会员",
    "description": "无限畅聊，解锁所有高级功能",
    "price": 29.99,
    "plan_type": "subscription",
    "message_credits": null,
    "duration_days": 30,
    "is_active": true
  }
}
```

**错误响应**:
- `400 Bad Request`: 套餐名称已存在、参数验证失败、业务规则违反
- `403 Forbidden`: 需要管理员权限

### 2. 更新套餐

**接口描述**: 更新指定套餐的信息

- **URL**: `PUT /plans/{id}`
- **认证**: 需要管理员权限

**路径参数**:
- `id`: 套餐ID（必须是正整数）

**请求参数**:
```json
{
  "name": "更新后的套餐名称",
  "description": "更新后的描述",
  "price": 39.99
}
```

**参数说明**:
- 所有字段都是可选的，只更新提供的字段
- 字段验证规则与创建接口相同
- 不能更改套餐类型（plan_type）

**业务规则**:
- 一次性套餐不能设置`duration_days`
- 订阅套餐不能设置`message_credits`
- 更新名称时检查唯一性

**响应示例**:
```json
{
  "code": 200,
  "message": "Plan updated successfully",
  "data": {
    "id": 5,
    "name": "更新后的套餐名称",
    "description": "更新后的描述",
    "price": 39.99,
    "plan_type": "subscription",
    "message_credits": null,
    "duration_days": 30,
    "is_active": true,
    "created_at": "2025-07-22T10:00:00.000Z",
    "updated_at": "2025-07-22T15:30:00.000Z"
  }
}
```

**错误响应**:
- `400 Bad Request`: 套餐名称已存在、没有提供更新数据、业务规则违反
- `404 Not Found`: 套餐不存在

### 3. 禁用套餐

**接口描述**: 禁用套餐（软删除，设置is_active为false）

- **URL**: `DELETE /plans/{id}`
- **认证**: 需要管理员权限

**路径参数**:
- `id`: 套餐ID（必须是正整数）

**响应示例**:
```json
{
  "code": 200,
  "message": "Plan disabled successfully",
  "data": null
}
```

**业务规则**:
- 软删除：将is_active设置为false，不物理删除数据
- 如果套餐有未完成的订单（status=pending），则无法禁用
- 已禁用的套餐无法再次禁用

**错误响应**:
- `400 Bad Request`: 套餐已被禁用、套餐有未完成的订单
- `404 Not Found`: 套餐不存在

---

## 使用示例

### JavaScript/Fetch API

```javascript
// 1. 创建订阅套餐
async function createSubscriptionPlan(token, planData) {
  try {
    const response = await fetch('/api/v1/admin/plans', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        name: planData.name,
        description: planData.description,
        price: planData.price,
        plan_type: 'subscription',
        duration_days: planData.duration_days
      })
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('订阅套餐创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建订阅套餐失败:', error);
    throw error;
  }
}

// 2. 创建一次性套餐
async function createOneTimePlan(token, planData) {
  try {
    const response = await fetch('/api/v1/admin/plans', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        name: planData.name,
        description: planData.description,
        price: planData.price,
        plan_type: 'one_time',
        message_credits: planData.message_credits
      })
    });

    const result = await response.json();
    if (result.code === 201) {
      console.log('一次性套餐创建成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('创建一次性套餐失败:', error);
    throw error;
  }
}

// 3. 更新套餐
async function updatePlan(token, planId, updateData) {
  try {
    const response = await fetch(`/api/v1/admin/plans/${planId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('套餐更新成功:', result.data);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('更新套餐失败:', error);
    throw error;
  }
}

// 4. 禁用套餐
async function disablePlan(token, planId) {
  try {
    const response = await fetch(`/api/v1/admin/plans/${planId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const result = await response.json();
    if (result.code === 200) {
      console.log('套餐禁用成功');
      return true;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('禁用套餐失败:', error);
    throw error;
  }
}

// 使用示例
async function example() {
  const adminToken = 'your-admin-token';

  // 创建订阅套餐
  const subscriptionPlan = await createSubscriptionPlan(adminToken, {
    name: '年度VIP会员',
    description: '年度超值套餐，享受更多优惠',
    price: 199.99,
    duration_days: 365
  });

  // 创建一次性套餐
  const oneTimePlan = await createOneTimePlan(adminToken, {
    name: '超级消息包',
    description: '一次性购买2000条消息额度',
    price: 89.99,
    message_credits: 2000
  });

  // 更新套餐
  const updatedPlan = await updatePlan(adminToken, subscriptionPlan.id, {
    description: '更新后的年度套餐描述',
    price: 179.99
  });

  // 禁用套餐
  await disablePlan(adminToken, oneTimePlan.id);
}
```

### Vue.js 套餐管理组件示例

```vue
<template>
  <div class="plan-management">
    <h2>套餐管理</h2>
    
    <!-- 创建套餐按钮 -->
    <button @click="showCreateModal = true" class="btn-create">
      创建新套餐
    </button>

    <!-- 套餐列表 -->
    <div class="plans-list">
      <div v-for="plan in plans" :key="plan.id" class="plan-item">
        <div class="plan-info">
          <h4>{{ plan.name }}</h4>
          <p>{{ plan.description }}</p>
          <div class="plan-details">
            <span class="price">¥{{ plan.price }}</span>
            <span class="type">{{ getPlanTypeText(plan.plan_type) }}</span>
            <span v-if="plan.plan_type === 'subscription'" class="duration">
              {{ plan.duration_days }}天
            </span>
            <span v-else class="credits">
              {{ plan.message_credits }}条消息
            </span>
          </div>
          <small>创建时间: {{ formatTime(plan.created_at) }}</small>
        </div>
        <div class="plan-actions">
          <button @click="editPlan(plan)" class="btn-edit">编辑</button>
          <button @click="disablePlan(plan)" class="btn-disable">禁用</button>
        </div>
      </div>
    </div>

    <!-- 创建/编辑套餐弹窗 -->
    <div v-if="showCreateModal || editingPlan" class="plan-modal">
      <div class="modal-content">
        <h3>{{ editingPlan ? '编辑套餐' : '创建套餐' }}</h3>
        <form @submit.prevent="savePlan">
          <div class="form-group">
            <label>套餐名称 *</label>
            <input v-model="planForm.name" required />
          </div>
          
          <div class="form-group">
            <label>套餐描述</label>
            <textarea v-model="planForm.description" rows="3"></textarea>
          </div>
          
          <div class="form-group">
            <label>价格 *</label>
            <input v-model.number="planForm.price" type="number" step="0.01" min="0" required />
          </div>
          
          <div class="form-group">
            <label>套餐类型 *</label>
            <select v-model="planForm.plan_type" required :disabled="editingPlan">
              <option value="">请选择</option>
              <option value="subscription">订阅套餐</option>
              <option value="one_time">一次性套餐</option>
            </select>
          </div>
          
          <div v-if="planForm.plan_type === 'subscription'" class="form-group">
            <label>有效期（天）*</label>
            <input v-model.number="planForm.duration_days" type="number" min="1" max="3650" required />
          </div>
          
          <div v-if="planForm.plan_type === 'one_time'" class="form-group">
            <label>消息点数 *</label>
            <input v-model.number="planForm.message_credits" type="number" min="1" max="999999" required />
          </div>
          
          <div class="form-actions">
            <button type="submit" :disabled="saving">
              {{ saving ? '保存中...' : '保存' }}
            </button>
            <button type="button" @click="closePlanModal">取消</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      plans: [],
      showCreateModal: false,
      editingPlan: null,
      planForm: {
        name: '',
        description: '',
        price: 0,
        plan_type: '',
        duration_days: null,
        message_credits: null
      },
      saving: false,
      adminToken: localStorage.getItem('adminToken')
    }
  },

  async mounted() {
    await this.loadPlans();
  },

  methods: {
    async loadPlans() {
      try {
        const response = await fetch('/api/v1/plans');
        const result = await response.json();
        if (result.code === 200) {
          this.plans = result.data;
        }
      } catch (error) {
        console.error('加载套餐失败:', error);
      }
    },

    async savePlan() {
      this.saving = true;
      try {
        const url = this.editingPlan 
          ? `/api/v1/admin/plans/${this.editingPlan.id}`
          : '/api/v1/admin/plans';
        
        const method = this.editingPlan ? 'PUT' : 'POST';

        // 构建请求数据
        const requestData = {
          name: this.planForm.name,
          description: this.planForm.description,
          price: this.planForm.price
        };

        if (!this.editingPlan) {
          // 创建时需要套餐类型和相关字段
          requestData.plan_type = this.planForm.plan_type;
          if (this.planForm.plan_type === 'subscription') {
            requestData.duration_days = this.planForm.duration_days;
          } else {
            requestData.message_credits = this.planForm.message_credits;
          }
        } else {
          // 更新时只更新允许的字段
          if (this.planForm.plan_type === 'subscription' && this.planForm.duration_days) {
            requestData.duration_days = this.planForm.duration_days;
          }
          if (this.planForm.plan_type === 'one_time' && this.planForm.message_credits) {
            requestData.message_credits = this.planForm.message_credits;
          }
        }

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.adminToken}`
          },
          body: JSON.stringify(requestData)
        });

        const result = await response.json();
        if (result.code === 200 || result.code === 201) {
          alert('保存成功');
          this.closePlanModal();
          await this.loadPlans();
        } else {
          alert('保存失败: ' + result.message);
        }
      } catch (error) {
        console.error('保存套餐失败:', error);
        alert('保存失败，请重试');
      } finally {
        this.saving = false;
      }
    },

    async disablePlan(plan) {
      if (!confirm(`确定要禁用套餐"${plan.name}"吗？`)) {
        return;
      }

      try {
        const response = await fetch(`/api/v1/admin/plans/${plan.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${this.adminToken}` }
        });

        const result = await response.json();
        if (result.code === 200) {
          alert('禁用成功');
          await this.loadPlans();
        } else {
          alert('禁用失败: ' + result.message);
        }
      } catch (error) {
        console.error('禁用套餐失败:', error);
        alert('禁用失败，请重试');
      }
    },

    editPlan(plan) {
      this.editingPlan = plan;
      this.planForm = { ...plan };
    },

    closePlanModal() {
      this.showCreateModal = false;
      this.editingPlan = null;
      this.planForm = {
        name: '',
        description: '',
        price: 0,
        plan_type: '',
        duration_days: null,
        message_credits: null
      };
    },

    getPlanTypeText(type) {
      return type === 'subscription' ? '订阅套餐' : '一次性套餐';
    },

    formatTime(timeString) {
      return new Date(timeString).toLocaleString();
    }
  }
}
</script>

<style scoped>
.plan-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.btn-create {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
}

.plan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 15px;
  background: white;
}

.plan-details {
  display: flex;
  gap: 15px;
  margin: 10px 0;
}

.price {
  font-size: 18px;
  font-weight: bold;
  color: #007bff;
}

.type {
  background: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.plan-actions {
  display: flex;
  gap: 10px;
}

.btn-edit {
  background-color: #007bff;
  color: white;
}

.btn-disable {
  background-color: #dc3545;
  color: white;
}

.plan-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}
</style>
```

---

## 数据库交互

### 套餐表结构
- **创建**: 检查名称唯一性，验证业务规则，插入新记录
- **更新**: 动态构建更新字段，支持部分更新，验证业务规则
- **禁用**: 软删除（is_active=false），检查订单依赖

### 业务规则验证
- 套餐类型与字段的一致性检查
- 名称唯一性验证
- 订单依赖检查

---

## 安全考虑

1. **权限验证**: 所有接口都需要管理员权限
2. **参数验证**: 严格的输入验证和格式检查
3. **业务规则**: 防止创建不一致的套餐配置
4. **软删除**: 保留数据完整性，避免级联删除问题
5. **依赖检查**: 防止删除有依赖关系的套餐

---

## 注意事项

1. **套餐类型**: 创建后不能更改套餐类型
2. **字段一致性**: 订阅套餐只能有duration_days，一次性套餐只能有message_credits
3. **软删除机制**: 禁用的套餐仍保留在数据库中
4. **订单依赖**: 有未完成订单的套餐无法禁用
5. **名称唯一性**: 套餐名称在活跃套餐中必须唯一

---

## 测试覆盖

套餐管理包含完整的测试覆盖：

### 创建套餐测试
- ✅ 创建订阅套餐测试（成功、字段验证）
- ✅ 创建一次性套餐测试（成功、字段验证）
- ✅ 业务规则测试（类型与字段一致性）
- ✅ 名称唯一性测试
- ✅ 权限验证测试

### 更新套餐测试
- ✅ 更新套餐测试（成功、部分更新）
- ✅ 业务规则验证测试
- ✅ 不存在资源测试
- ✅ 参数验证测试

### 禁用套餐测试
- ✅ 禁用套餐测试（成功、重复禁用）
- ✅ 依赖检查测试
- ✅ 不存在资源测试

所有12个测试用例均已通过验证，套餐管理功能完整可用。
