const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const { errorHandler } = require('./middleware/errorHandler');
const { responseFormatter } = require('./middleware/responseFormatter');

const app = express();

// 安全中间件
app.use(helmet());
app.use(cors());

// 限流中间件
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    code: 42901,
    message: '请求过于频繁，请稍后再试',
    data: null
  }
});
app.use(limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 响应格式化中间件
app.use(responseFormatter);

// 健康检查
app.get('/health', (req, res) => {
  res.success('服务运行正常');
});

// API路由
app.use('/api/v1/auth', authRoutes);

// 404处理
app.use('*', (req, res) => {
  res.error('接口不存在', 40401);
});

// 错误处理中间件
app.use(errorHandler);

const PORT = process.env.PORT || 3000;

// 只有在非测试环境下才启动服务器
if (process.env.NODE_ENV !== 'test') {
  const { initDatabase } = require('./config/database');

  initDatabase().then(() => {
    app.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
      console.log(`环境: ${process.env.NODE_ENV}`);
    });
  }).catch(error => {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  });
}

module.exports = app;
