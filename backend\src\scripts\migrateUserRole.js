const { query } = require('../config/database');

async function migrateUserRole() {
  try {
    console.log('开始迁移用户表，添加role字段...');
    
    // 检查role字段是否已存在
    const columns = await query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'users' 
      AND COLUMN_NAME = 'role'
    `);
    
    if (columns.length > 0) {
      console.log('role字段已存在，跳过迁移');
      return;
    }
    
    // 添加role字段
    await query(`
      ALTER TABLE users 
      ADD COLUMN role ENUM('user', 'admin') NOT NULL DEFAULT 'user' COMMENT '用户角色' 
      AFTER is_guest
    `);
    
    console.log('role字段添加成功');
    
    // 检查是否已有管理员账户
    const existingAdmins = await query('SELECT COUNT(*) as count FROM users WHERE role = "admin"');
    if (existingAdmins[0].count > 0) {
      console.log('管理员账户已存在，跳过创建');
      return;
    }
    
    const { v4: uuidv4 } = require('uuid');
    const { hashPassword } = require('../utils/password');
    
    // 创建默认管理员账户
    const adminUuid = uuidv4();
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123456';
    const hashedPassword = await hashPassword(adminPassword);
    
    await query(
      'INSERT INTO users (uuid, email, password_hash, is_guest, role, status) VALUES (?, ?, ?, ?, ?, ?)',
      [adminUuid, adminEmail, hashedPassword, false, 'admin', 'active']
    );
    
    console.log('默认管理员账户创建完成');
    console.log(`管理员邮箱: ${adminEmail}`);
    console.log(`管理员密码: ${adminPassword}`);
    console.log('请在生产环境中修改默认密码！');
    
  } catch (error) {
    console.error('用户表迁移失败:', error);
    throw error;
  }
}

async function main() {
  try {
    await migrateUserRole();
    console.log('用户表迁移完成！');
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

main();
