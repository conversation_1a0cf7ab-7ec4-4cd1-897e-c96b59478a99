const request = require('supertest');
const app = require('../src/app');

describe('调试支付回调功能', () => {
  let userToken;
  let orderUuid;
  const testEmail = `debug_payment_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 注册测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    if (registerResponse.body.code === 201) {
      userToken = registerResponse.body.data.token;
    }

    // 获取套餐列表
    const plansResponse = await request(app)
      .get('/api/v1/plans');

    console.log('套餐列表:', JSON.stringify(plansResponse.body, null, 2));

    if (plansResponse.body.data && plansResponse.body.data.length > 0) {
      const subscriptionPlan = plansResponse.body.data.find(plan => plan.plan_type === 'subscription');
      if (subscriptionPlan) {
        // 创建订单
        const orderResponse = await request(app)
          .post('/api/v1/orders')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            plan_id: subscriptionPlan.id
          });

        console.log('订单创建响应:', JSON.stringify(orderResponse.body, null, 2));

        if (orderResponse.body.code === 201) {
          orderUuid = orderResponse.body.data.order_uuid;
        }
      }
    }
  });

  test('测试支付回调接口', async () => {
    if (!orderUuid) {
      console.log('跳过测试：订单创建失败');
      return;
    }

    console.log('测试订单UUID:', orderUuid);

    const response = await request(app)
      .post('/api/v1/payments/notify')
      .send({
        order_uuid: orderUuid,
        status: 'success',
        transaction_id: `txn_${Date.now()}`
      });

    console.log('支付回调响应状态:', response.status);
    console.log('支付回调响应体:', JSON.stringify(response.body, null, 2));

    if (response.status !== 200) {
      console.log('支付回调错误详情:', response.text);
    }
  });

  test('测试支付回调后的用户状态', async () => {
    if (!userToken) {
      console.log('跳过测试：用户注册失败');
      return;
    }

    const response = await request(app)
      .get('/api/v1/user/profile')
      .set('Authorization', `Bearer ${userToken}`);

    console.log('支付后用户状态:', JSON.stringify(response.body, null, 2));
  });
});
