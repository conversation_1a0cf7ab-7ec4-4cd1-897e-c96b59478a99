# AI伙伴聊天应用 - API使用建议与最佳实践

## 概述

本文档详细描述了AI伙伴聊天应用的API使用建议与最佳实践的完整实现。

## 1. 游客转正功能

### 功能描述
当游客用户选择注册时，系统会将其现有的游客账户转换为正式注册账户，保留所有聊天记录和数据。

### 实现方式

#### 前端调用
```javascript
// 游客转正注册
async function convertGuestToUser(email, password, guestToken) {
  try {
    const response = await fetch('/api/v1/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-guest-token': guestToken // 在请求头中传递游客token
      },
      body: JSON.stringify({
        email,
        password
      })
    });

    const result = await response.json();
    
    if (result.code === 201) {
      if (result.data.converted) {
        console.log('游客转正成功，保留了所有聊天记录');
      } else {
        console.log('新用户注册成功');
      }
      
      // 更新本地存储的token
      localStorage.setItem('authToken', result.data.token);
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
}
```

#### 后端实现
- **服务层**: `src/services/userService.js` - `registerUser()` 函数支持游客转正
- **控制器**: `src/controllers/authController.js` - 从请求头获取游客token
- **验证**: 验证游客token有效性，检查邮箱唯一性
- **数据保留**: 更新现有用户记录而非创建新记录

### 业务规则
- 游客token必须有效且对应真实的游客用户
- 邮箱不能与其他用户重复
- 转正后保留所有聊天会话和消息记录
- 生成新的JWT token，包含完整用户信息

---

## 2. 每日额度重置功能

### 功能描述
自动管理用户的每日免费消息额度，支持定时重置和实时检查。

### 实现方式

#### 定时任务
```bash
# 添加到crontab，每天凌晨00:01执行
1 0 * * * /usr/bin/node /path/to/backend/src/scripts/dailyQuotaReset.js
```

#### 实时检查
```javascript
const { checkQuotaAvailable, consumeQuota } = require('../utils/dailyQuota');

// 发送消息前检查额度
async function sendMessage(userId, content) {
  // 检查额度
  const quotaCheck = await checkQuotaAvailable(userId);
  if (!quotaCheck.available) {
    throw new Error('QUOTA_EXCEEDED');
  }

  // 处理消息...
  
  // 消费额度
  await consumeQuota(userId, 1);
}
```

#### 核心工具函数
- **每日重置**: `checkAndResetDailyQuota()` - 检查并重置用户每日额度
- **额度消费**: `consumeQuota()` - 优先使用点数，再使用免费额度
- **额度查询**: `getDailyQuotaInfo()` - 获取用户当前额度信息
- **批量重置**: `batchResetDailyQuota()` - 定时任务批量重置

### 业务规则
- 游客用户：每日10条免费消息
- 注册用户：每日50条免费消息
- 优先消费购买的消息点数
- 每日凌晨自动重置免费额度
- 支持实时检查和重置

---

## 3. 安全性增强

### 输入验证与清理

#### XSS防护
```javascript
const { sanitizeHtml, sanitizeText } = require('../utils/security');

// HTML内容清理
const cleanHtml = sanitizeHtml(userInput, {
  ALLOWED_TAGS: ['p', 'br', 'strong', 'em'],
  ALLOWED_ATTR: []
});

// 文本内容清理
const cleanText = sanitizeText(userInput, {
  maxLength: 2000,
  allowNewlines: true,
  allowSpecialChars: false
});
```

#### 密码安全
```javascript
const { validatePassword } = require('../utils/security');

const validation = validatePassword(password);
if (!validation.valid) {
  return res.error(validation.errors.join(', '), 40001, 400);
}

// 密码要求：
// - 至少8位长度
// - 包含大小写字母
// - 包含数字
// - 不能是常见弱密码
```

#### 频率限制
```javascript
const { loginRateLimit, chatRateLimit } = require('../middleware/security');

// 登录频率限制：每15分钟最多5次
router.post('/login', loginRateLimit(), authController.login);

// 聊天频率限制：每分钟最多30条消息
router.post('/messages', chatRateLimit(), chatController.sendMessage);
```

### 安全中间件
- **输入清理**: `sanitizeInput()` - 自动清理请求体和查询参数
- **安全头**: `securityHeaders()` - 设置安全相关HTTP头
- **请求大小限制**: `requestSizeLimit()` - 限制请求体大小
- **SQL注入防护**: 使用参数化查询，输入清理

---

## 4. 性能优化

### 分页实现

#### 通用分页工具
```javascript
const { createPagination, paginateMessages } = require('../utils/pagination');

// 创建分页实例
const pagination = createPagination(req.query, {
  maxLimit: 100,
  defaultLimit: 20
});

// 执行分页查询
const result = await paginateMessages(sessionId, req.query, query);
```

#### 聊天消息分页
```javascript
// GET /api/v1/chat/sessions/:uuid/messages?page=1&limit=20
async function getMessages(req, res) {
  const { session_uuid } = req.params;
  const result = await paginateMessages(session_uuid, req.query, query);
  
  res.success(result.data, 'Messages retrieved successfully', 200, {
    pagination: result.pagination
  });
}
```

#### 响应格式
```json
{
  "code": 200,
  "message": "Success",
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false,
    "nextPage": 2,
    "prevPage": null
  }
}
```

### 数据库优化
- **索引优化**: 为常用查询字段添加索引
- **查询优化**: 使用LIMIT/OFFSET进行分页
- **连接池**: 合理配置数据库连接池
- **缓存策略**: 对频繁查询的数据进行缓存

---

## 5. 异步处理

### 消息队列系统

#### 基本使用
```javascript
const { queue } = require('../utils/messageQueue');

// 添加任务到队列
queue.enqueue('payment-callback', {
  orderNumber: 'ORDER_123',
  amount: 29.99,
  status: 'completed'
}, {
  priority: 1,
  maxRetries: 3
});

// 注册处理器
queue.process('payment-callback', async (data, task) => {
  await processPaymentCallback(data);
}, {
  concurrency: 2,
  timeout: 10000
});
```

#### 支付回调异步处理
```javascript
// 支付回调接口立即返回
app.post('/api/v1/payment/callback', (req, res) => {
  // 立即响应支付平台
  res.status(200).send('OK');
  
  // 异步处理回调
  queue.enqueue('payment-callback', req.body);
});
```

#### 预定义队列
- **payment-callback**: 支付回调处理
- **email**: 邮件发送
- **statistics**: 数据统计
- **notification**: 通知推送

### 队列特性
- **优先级**: 支持任务优先级排序
- **重试机制**: 指数退避重试策略
- **并发控制**: 可配置并发处理数量
- **超时处理**: 任务执行超时保护
- **监控**: 队列状态监控和告警

---

## 6. 监控与日志

### 队列监控
```javascript
// 获取队列状态
const status = queue.getAllQueuesStatus();
console.log('队列状态:', status);

// 监听队列事件
queue.on('taskCompleted', ({ queueName, taskId }) => {
  console.log(`任务完成: ${queueName}/${taskId}`);
});

queue.on('taskExhausted', ({ queueName, taskId, error }) => {
  console.error(`任务彻底失败: ${queueName}/${taskId}`, error);
  // 发送告警通知
});
```

### 性能监控
```javascript
// 响应时间监控
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.path} - ${duration}ms`);
  });
  
  next();
});
```

---

## 7. 部署建议

### 环境变量配置
```bash
# 每日额度配置
GUEST_DAILY_LIMIT=10
USER_DAILY_LIMIT=50

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d
JWT_GUEST_EXPIRES_IN=1d

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
```

### 定时任务配置
```bash
# 每日额度重置（每天凌晨00:01）
1 0 * * * /usr/bin/node /path/to/backend/src/scripts/dailyQuotaReset.js

# 清理过期会话（每周日凌晨02:00）
0 2 * * 0 /usr/bin/node /path/to/backend/src/scripts/cleanupSessions.js

# 数据库备份（每天凌晨03:00）
0 3 * * * /usr/bin/mysqldump ai_companion > /backup/ai_companion_$(date +\%Y\%m\%d).sql
```

### 生产环境优化
- **负载均衡**: 使用Nginx进行负载均衡
- **缓存**: Redis缓存热点数据
- **消息队列**: 使用Redis或RabbitMQ替代内存队列
- **监控**: 集成APM工具（如New Relic、DataDog）
- **日志**: 结构化日志和集中式日志管理

---

## 8. 测试覆盖

### 最佳实践测试
- ✅ 游客转正功能测试（数据保留、邮箱验证）
- ✅ 每日额度重置测试（定时任务、实时检查）
- ✅ 安全性测试（输入清理、密码验证、频率限制）
- ✅ 分页功能测试（参数验证、元数据计算）
- ✅ 异步处理测试（队列任务、重试机制）

### 性能测试
- ✅ 并发请求测试
- ✅ 大数据量分页测试
- ✅ 频率限制压力测试
- ✅ 消息队列性能测试

所有最佳实践功能均已通过完整测试验证，确保生产环境可用性。

---

## 总结

本最佳实践实现涵盖了：

1. **用户体验优化**: 游客转正保留数据
2. **资源管理**: 每日额度自动重置
3. **安全防护**: 多层次安全验证
4. **性能优化**: 分页和缓存策略
5. **系统可靠性**: 异步处理和重试机制

这些实践确保了系统的安全性、性能和用户体验，为生产环境部署提供了坚实的基础。
