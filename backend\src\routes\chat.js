const express = require('express');
const chatController = require('../controllers/chatController');
const { authenticateToken } = require('../middleware/auth');
const { validate, validateParams, createSessionSchema, sendMessageSchema, sessionUuidSchema } = require('../validators/chat');

const router = express.Router();

// 所有聊天接口都需要认证
router.use(authenticateToken);

/**
 * @route POST /api/v1/chat/sessions
 * @desc 创建新的聊天会话
 * @access Private
 */
router.post('/sessions', validate(createSessionSchema), chatController.createSession);

/**
 * @route GET /api/v1/chat/sessions
 * @desc 获取用户的所有聊天会话
 * @access Private
 */
router.get('/sessions', chatController.getSessions);

/**
 * @route POST /api/v1/chat/sessions/:session_uuid/messages
 * @desc 发送消息到指定会话（非流式）
 * @access Private
 */
router.post('/sessions/:session_uuid/messages',
  validateParams(sessionUuidSchema),
  validate(sendMessageSchema),
  chatController.sendMessage
);

/**
 * @route POST /api/v1/chat/sessions/:session_uuid/messages/stream
 * @desc 发送消息到指定会话（流式响应）
 * @access Private
 */
router.post('/sessions/:session_uuid/messages/stream',
  validateParams(sessionUuidSchema),
  validate(sendMessageSchema),
  chatController.sendMessageStream
);

/**
 * @route GET /api/v1/chat/sessions/:session_uuid/messages
 * @desc 获取指定会话的历史消息
 * @access Private
 */
router.get('/sessions/:session_uuid/messages',
  validateParams(sessionUuidSchema),
  chatController.getMessages
);

/**
 * @route DELETE /api/v1/chat/sessions/:session_uuid
 * @desc 删除指定的聊天会话
 * @access Private
 */
router.delete('/sessions/:session_uuid',
  validateParams(sessionUuidSchema),
  chatController.deleteSession
);

/**
 * @route DELETE /api/v1/chat/sessions/:session_uuid/messages
 * @desc 清空指定会话的消息历史
 * @access Private
 */
router.delete('/sessions/:session_uuid/messages',
  validateParams(sessionUuidSchema),
  chatController.clearSessionMessages
);

/**
 * @route DELETE /api/v1/chat/sessions
 * @desc 删除用户的所有聊天记录
 * @access Private
 */
router.delete('/sessions', chatController.deleteAllChats);

module.exports = router;
