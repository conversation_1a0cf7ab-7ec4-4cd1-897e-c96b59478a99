const request = require('supertest');
const app = require('../src/app');

describe('认证模块测试', () => {
  let guestToken;
  let userToken;
  const testEmail = `test${Date.now()}@example.com`;
  const testPassword = 'test123456';

  describe('POST /api/v1/auth/guest', () => {
    test('应该成功创建游客账户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/guest')
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.message).toBe('Guest user created successfully');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.is_guest).toBe(true);
      expect(response.body.data.user).toHaveProperty('uuid');

      guestToken = response.body.data.token;
    });
  });

  describe('POST /api/v1/auth/register', () => {
    test('应该成功注册用户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.message).toBe('User registered successfully');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.email).toBe(testEmail);
      expect(response.body.data.user.is_guest).toBe(false);

      userToken = response.body.data.token;
    });

    test('应该拒绝重复邮箱注册', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(409);

      expect(response.body.code).toBe(40901);
      expect(response.body.message).toBe('邮箱已存在');
    });

    test('应该拒绝无效邮箱格式', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: 'invalid-email',
          password: testPassword
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝弱密码', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: '<EMAIL>',
          password: '123'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('POST /api/v1/auth/login', () => {
    test('应该成功登录', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testEmail,
          password: testPassword
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Login successful');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.email).toBe(testEmail);
    });

    test('应该拒绝错误密码', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: testEmail,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.code).toBe(40101);
      expect(response.body.message).toBe('邮箱或密码错误');
    });

    test('应该拒绝不存在的邮箱', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          email: '<EMAIL>',
          password: testPassword
        })
        .expect(401);

      expect(response.body.code).toBe(40101);
      expect(response.body.message).toBe('邮箱或密码错误');
    });
  });

  describe('GET /api/v1/auth/me', () => {
    test('应该返回游客用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${guestToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data.is_guest).toBe(true);
    });

    test('应该返回注册用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.data.email).toBe(testEmail);
      expect(response.body.data.is_guest).toBe(false);
    });

    test('应该拒绝无效token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });

    test('应该拒绝缺少token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });
});
