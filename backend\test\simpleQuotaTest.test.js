const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');
const { incrementDailyMessageCount } = require('../src/utils/dailyQuota');

describe('简单额度测试', () => {
  let normalUserId;

  beforeAll(async () => {
    // 创建普通用户
    const testEmail = `simple_quota_${Date.now()}@example.com`;
    const userResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: 'test123456'
      });

    if (userResponse.body.code === 201) {
      const normalUsers = await query('SELECT id FROM users WHERE email = ?', [testEmail]);
      normalUserId = normalUsers[0].id;
      
      // 确保没有订阅记录
      await query('DELETE FROM user_subscriptions WHERE user_id = ?', [normalUserId]);
      
      // 重置用户状态
      await query('UPDATE users SET daily_message_count = 0, message_credits = 0, last_usage_date = CURRENT_DATE WHERE id = ?', [normalUserId]);
    }
  });

  test('测试数据库更新是否正常', async () => {
    if (!normalUserId) {
      console.log('跳过测试：用户创建失败');
      return;
    }

    console.log('=== 测试数据库更新 ===');
    
    // 1. 检查初始状态
    let users = await query('SELECT daily_message_count, last_usage_date FROM users WHERE id = ?', [normalUserId]);
    console.log('1. 初始状态:', users[0]);
    
    // 2. 手动增加1次
    console.log('2. 手动增加1次...');
    const newCount1 = await incrementDailyMessageCount(normalUserId, 1);
    console.log('2. 返回的新计数:', newCount1);
    
    // 3. 检查数据库状态
    users = await query('SELECT daily_message_count, last_usage_date FROM users WHERE id = ?', [normalUserId]);
    console.log('3. 数据库状态:', users[0]);
    
    // 4. 再增加1次
    console.log('4. 再增加1次...');
    const newCount2 = await incrementDailyMessageCount(normalUserId, 1);
    console.log('4. 返回的新计数:', newCount2);
    
    // 5. 检查数据库状态
    users = await query('SELECT daily_message_count, last_usage_date FROM users WHERE id = ?', [normalUserId]);
    console.log('5. 数据库状态:', users[0]);
    
    // 6. 连续增加5次
    console.log('6. 连续增加5次...');
    for (let i = 0; i < 5; i++) {
      const count = await incrementDailyMessageCount(normalUserId, 1);
      console.log(`6.${i+1}. 增加后计数:`, count);
    }
    
    // 7. 最终检查
    users = await query('SELECT daily_message_count, last_usage_date FROM users WHERE id = ?', [normalUserId]);
    console.log('7. 最终数据库状态:', users[0]);
    
    console.log('=== 测试结束 ===');
    
    // 验证最终计数应该是7
    expect(users[0].daily_message_count).toBe(7);
  });

  afterAll(async () => {
    // 清理测试数据
    if (normalUserId) {
      await query('DELETE FROM users WHERE id = ?', [normalUserId]);
    }
  });
});
