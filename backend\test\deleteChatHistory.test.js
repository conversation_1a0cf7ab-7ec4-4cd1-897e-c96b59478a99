const request = require('supertest');
const app = require('../src/app');
const { query } = require('../src/config/database');

describe('删除聊天历史记录测试', () => {
  let userToken;
  let sessionUuid1;
  let sessionUuid2;
  let userId;
  const testEmail = `delete_chat_test_${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 创建测试用户
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;
    userId = registerResponse.body.data.user.id;

    // 获取AI角色
    const rolesResponse = await request(app)
      .get('/api/v1/ai-roles');

    const validRoleId = rolesResponse.body.data[0].id;

    // 创建两个测试聊天会话
    const session1Response = await request(app)
      .post('/api/v1/chat/sessions')
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        ai_role_id: validRoleId
      });

    sessionUuid1 = session1Response.body.data.uuid;

    const session2Response = await request(app)
      .post('/api/v1/chat/sessions')
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        ai_role_id: validRoleId
      });

    sessionUuid2 = session2Response.body.data.uuid;

    // 在每个会话中发送一些消息
    await request(app)
      .post(`/api/v1/chat/sessions/${sessionUuid1}/messages`)
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        content: '测试消息1'
      });

    await request(app)
      .post(`/api/v1/chat/sessions/${sessionUuid2}/messages`)
      .set('Authorization', `Bearer ${userToken}`)
      .send({
        content: '测试消息2'
      });

    // 等待消息处理完成
    await new Promise(resolve => setTimeout(resolve, 2000));
  });

  describe('DELETE /api/v1/me/chats/:session_uuid', () => {
    test('用户应该能删除指定的聊天会话', async () => {
      // 删除前检查会话存在
      const beforeSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE user_id = ?',
        [userId]
      );
      expect(beforeSessions[0].count).toBe(2);

      // 删除指定会话
      const response = await request(app)
        .delete(`/api/v1/me/chats/${sessionUuid1}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Chat session deleted successfully');
      expect(response.body.data).toBeNull();

      // 检查会话已被删除
      const afterSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE user_id = ?',
        [userId]
      );
      expect(afterSessions[0].count).toBe(1);

      // 检查会话消息也被删除
      const messages = await query(
        'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = (SELECT id FROM chat_sessions WHERE uuid = ?)',
        [sessionUuid1]
      );
      expect(messages[0].count).toBe(0);

      // 检查另一个会话仍然存在
      const remainingSessions = await query(
        'SELECT uuid FROM chat_sessions WHERE user_id = ?',
        [userId]
      );
      expect(remainingSessions.length).toBe(1);
      expect(remainingSessions[0].uuid).toBe(sessionUuid2);
    });

    test('应该拒绝删除不存在的会话', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      
      const response = await request(app)
        .delete(`/api/v1/me/chats/${fakeUuid}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('聊天会话不存在或无权限访问');
    });

    test('应该拒绝无效的UUID格式', async () => {
      const response = await request(app)
        .delete('/api/v1/me/chats/invalid-uuid')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .delete(`/api/v1/me/chats/${sessionUuid2}`)
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('DELETE /api/v1/me/chats', () => {
    test('用户应该能删除所有聊天历史记录', async () => {
      // 删除前检查还有会话存在
      const beforeSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE user_id = ?',
        [userId]
      );
      expect(beforeSessions[0].count).toBe(1);

      // 删除所有聊天历史
      const response = await request(app)
        .delete('/api/v1/me/chats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('All chat history deleted successfully');
      expect(response.body.data).toBeNull();

      // 检查所有会话都被删除
      const afterSessions = await query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE user_id = ?',
        [userId]
      );
      expect(afterSessions[0].count).toBe(0);

      // 检查所有消息都被删除
      const messages = await query(
        'SELECT COUNT(*) as count FROM chat_messages cm JOIN chat_sessions cs ON cm.session_id = cs.id WHERE cs.user_id = ?',
        [userId]
      );
      expect(messages[0].count).toBe(0);
    });

    test('删除空的聊天历史应该成功', async () => {
      // 再次删除（此时已经没有聊天记录）
      const response = await request(app)
        .delete('/api/v1/me/chats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('All chat history deleted successfully');
    });

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .delete('/api/v1/me/chats')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('权限隔离测试', () => {
    test('用户不能删除其他用户的聊天会话', async () => {
      // 创建另一个用户
      const otherUserEmail = `other_user_${Date.now()}@example.com`;
      const otherUserResponse = await request(app)
        .post('/api/v1/auth/register')
        .send({
          email: otherUserEmail,
          password: testPassword
        });

      const otherUserToken = otherUserResponse.body.data.token;

      // 为另一个用户创建会话
      const rolesResponse = await request(app)
        .get('/api/v1/ai-roles');

      const validRoleId = rolesResponse.body.data[0].id;

      const otherSessionResponse = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${otherUserToken}`)
        .send({
          ai_role_id: validRoleId
        });

      const otherSessionUuid = otherSessionResponse.body.data.uuid;

      // 尝试用第一个用户的token删除第二个用户的会话
      const response = await request(app)
        .delete(`/api/v1/me/chats/${otherSessionUuid}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('聊天会话不存在或无权限访问');
    });
  });
});
