const Joi = require('joi');

/**
 * 创建聊天会话验证
 */
const createSessionSchema = Joi.object({
  ai_role_id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': 'AI角色ID必须是数字',
      'number.integer': 'AI角色ID必须是整数',
      'number.positive': 'AI角色ID必须是正数',
      'any.required': 'AI角色ID是必填项'
    })
});

/**
 * 发送消息验证
 */
const sendMessageSchema = Joi.object({
  content: Joi.string()
    .trim()
    .min(1)
    .max(2000)
    .required()
    .messages({
      'string.empty': '消息内容不能为空',
      'string.min': '消息内容不能为空',
      'string.max': '消息内容不能超过2000个字符',
      'any.required': '消息内容是必填项'
    })
});

/**
 * 会话UUID验证
 */
const sessionUuidSchema = Joi.object({
  session_uuid: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': '会话ID格式无效',
      'any.required': '会话ID是必填项'
    })
});

/**
 * 验证中间件工厂函数
 */
function validate(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

/**
 * 验证路径参数中间件工厂函数
 */
function validateParams(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.params);
    if (error) {
      return res.error(error.details[0].message, 40001, 400);
    }
    next();
  };
}

module.exports = {
  createSessionSchema,
  sendMessageSchema,
  sessionUuidSchema,
  validate,
  validateParams
};
