const aiRoleService = require('../services/aiRoleService');

/**
 * 获取AI角色列表
 */
async function getAiRoles(req, res, next) {
  try {
    const roles = await aiRoleService.getAllActiveAiRoles();
    
    res.success(roles, 'Success');
  } catch (error) {
    next(error);
  }
}

/**
 * 获取AI角色详情
 */
async function getAiRoleById(req, res, next) {
  try {
    const { id } = req.params;
    
    const role = await aiRoleService.getAiRoleById(parseInt(id));
    
    if (!role) {
      return res.error('AI Role not found', 40401, 404);
    }
    
    res.success(role, 'Success');
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getAiRoles,
  getAiRoleById
};
