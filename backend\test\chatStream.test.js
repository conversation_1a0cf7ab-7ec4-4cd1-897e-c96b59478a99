const request = require('supertest');
const app = require('../src/app');

describe('流式聊天模块测试', () => {
  let userToken;
  let sessionUuid;
  let validRoleId;

  beforeAll(async () => {
    // 创建测试用户并获取token
    const testEmail = `streamtest${Date.now()}@example.com`;
    const testPassword = 'test123456';

    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;

    // 获取一个有效的AI角色ID
    const rolesResponse = await request(app)
      .get('/api/v1/ai-roles');

    if (rolesResponse.body.data.length > 0) {
      validRoleId = rolesResponse.body.data[0].id;
    }

    // 创建测试会话
    if (validRoleId) {
      const sessionResponse = await request(app)
        .post('/api/v1/chat/sessions')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          ai_role_id: validRoleId
        });

      sessionUuid = sessionResponse.body.data.uuid;
    }
  });

  describe('POST /api/v1/chat/sessions/:session_uuid/messages/stream', () => {
    test('应该成功建立SSE连接并接收流式数据', (done) => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        done();
        return;
      }

      const req = request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages/stream`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '请简单介绍一下你自己'
        });

      let receivedData = [];
      let connected = false;
      let completed = false;

      req.expect(200)
        .expect('Content-Type', /text\/event-stream/)
        .buffer(false)
        .parse((res, callback) => {
          let buffer = '';
          
          res.on('data', (chunk) => {
            buffer += chunk.toString();
            
            // 处理完整的事件
            const lines = buffer.split('\n\n');
            buffer = lines.pop(); // 保留不完整的最后一行
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                
                if (data === '[DONE]') {
                  completed = true;
                  callback(null, receivedData);
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  receivedData.push(parsed);
                  
                  if (parsed.type === 'connected') {
                    connected = true;
                  }
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }
          });

          res.on('end', () => {
            if (!completed) {
              callback(null, receivedData);
            }
          });

          res.on('error', (err) => {
            callback(err);
          });
        })
        .end((err, res) => {
          if (err) {
            console.error('SSE请求失败:', err);
            done(err);
            return;
          }

          try {
            // 验证连接建立
            expect(connected).toBe(true);
            
            // 验证接收到数据
            expect(receivedData.length).toBeGreaterThan(0);
            
            // 验证数据格式
            const connectedEvent = receivedData.find(d => d.type === 'connected');
            expect(connectedEvent).toBeDefined();
            
            // 如果有chunk事件，验证格式
            const chunkEvents = receivedData.filter(d => d.type === 'chunk');
            if (chunkEvents.length > 0) {
              expect(chunkEvents[0]).toHaveProperty('content');
              expect(chunkEvents[0]).toHaveProperty('fullContent');
            }
            
            // 如果有完成事件，验证格式
            const completeEvent = receivedData.find(d => d.type === 'complete');
            if (completeEvent) {
              expect(completeEvent).toHaveProperty('message');
              expect(completeEvent.message).toHaveProperty('role', 'assistant');
              expect(completeEvent.message).toHaveProperty('content');
            }
            
            // 如果有错误事件，记录但不失败（可能是LLM服务问题）
            const errorEvent = receivedData.find(d => d.type === 'error');
            if (errorEvent) {
              console.log('收到错误事件:', errorEvent);
            }
            
            done();
          } catch (assertError) {
            done(assertError);
          }
        });
    }, 30000); // 增加超时时间到30秒

    test('应该拒绝无效的会话UUID', (done) => {
      request(app)
        .post('/api/v1/chat/sessions/invalid-uuid/messages/stream')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '测试消息'
        })
        .expect(400)
        .end((err, res) => {
          if (err) {
            done(err);
            return;
          }
          
          expect(res.body.code).toBe(40001);
          done();
        });
    });

    test('应该拒绝空消息', (done) => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        done();
        return;
      }

      request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages/stream`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: ''
        })
        .expect(400)
        .end((err, res) => {
          if (err) {
            done(err);
            return;
          }
          
          expect(res.body.code).toBe(40001);
          done();
        });
    });

    test('应该拒绝缺少认证token', (done) => {
      if (!sessionUuid) {
        console.log('跳过测试：没有可用的会话');
        done();
        return;
      }

      request(app)
        .post(`/api/v1/chat/sessions/${sessionUuid}/messages/stream`)
        .send({
          content: '测试消息'
        })
        .expect(401)
        .end((err, res) => {
          if (err) {
            done(err);
            return;
          }
          
          expect(res.body.code).toBe(40101);
          done();
        });
    });

    test('应该拒绝不存在的会话', (done) => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      
      const req = request(app)
        .post(`/api/v1/chat/sessions/${fakeUuid}/messages/stream`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          content: '测试消息'
        });

      let receivedError = false;

      req.expect(200)
        .buffer(false)
        .parse((res, callback) => {
          let buffer = '';
          
          res.on('data', (chunk) => {
            buffer += chunk.toString();
            
            const lines = buffer.split('\n\n');
            buffer = lines.pop();
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                
                if (data === '[DONE]') {
                  callback(null, { receivedError });
                  return;
                }
                
                try {
                  const parsed = JSON.parse(data);
                  if (parsed.type === 'error' && parsed.code === 40401) {
                    receivedError = true;
                  }
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }
          });

          res.on('end', () => {
            callback(null, { receivedError });
          });
        })
        .end((err, res) => {
          if (err) {
            done(err);
            return;
          }

          expect(receivedError).toBe(true);
          done();
        });
    }, 10000);
  });
});
