
# **AI 伙伴聊天应用 - RESTful API 设计文档**

  - **Version:** 1.0
  - **Base URL:** `/api/v1`

## **通用规范 (General Specifications)**

### 1\. **统一响应格式 (Unified Response Format)**

所有 API 响应都将遵循统一的 JSON 结构，以便客户端进行统一处理。

**成功响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

**失败响应:**

```json
{
  "code": 40401, // 业务错误码
  "message": "资源未找到", // 错误描述
  "data": null
}
```

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `code` | `Integer` | 状态码。`200` 表示成功，其他表示各类错误。 |
| `message`| `String` | 对 `code` 的文本描述，用于调试或提示。 |
| `data` | `Object` or `Array` or `null` | 成功时返回的业务数据。失败时为 `null`。 |

### 2\. **认证机制 (Authentication)**

本系统采用 `JWT (JSON Web Token)` 进行用户认证。

  * **公开接口 (Public):** 无需认证即可访问。
  * **认证接口 (Authenticated):** 需要在 HTTP 请求头中携带 `Authorization` 字段。

**Header 示例:**

```
Authorization: Bearer <your_jwt_token>
```

登录或注册成功后，服务器会返回 `token`。客户端应妥善保存，并在后续请求中携带。

### 3\. **统一错误码 (Error Codes)**

| HTTP 状态码 | 业务错误码 (`code`) | 描述 |
| :--- | :--- | :--- |
| `200` | `200` | 请求成功 (Success) |
| `201` | `201` | 资源创建成功 (Created) |
| `400` | `40001` | 请求参数无效 (Bad Request) |
| `401` | `40101` | 未授权或 Token 无效 (Unauthorized) |
| `401` | `40102` | Token 已过期 (Token Expired) |
| `403` | `40301` | 禁止访问，权限不足 (Forbidden) |
| `404` | `40401` | 请求的资源未找到 (Not Found) |
| `409` | `40901` | 资源冲突，如邮箱已注册 (Conflict) |
| `500` | `50001` | 服务器内部错误 (Internal Server Error) |

-----

## **模块一：用户认证与账户基础 (Authentication & Account)**

此模块负责处理系统的用户入口，包括游客创建、用户注册和登录。

### 1\. 创建游客账户

  * **功能名称:** 创建一个临时的游客账户。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/auth/guest`
  * **认证要求:** Public
  * **参数说明:** 无
  * **成功响应 (201):**
    ```json
    {
        "code": 201,
        "message": "Guest user created successfully",
        "data": {
            "user": {
                "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                "is_guest": true
            },
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        }
    }
    ```

### 2\. 用户注册

  * **功能名称:** 使用邮箱和密码注册一个正式账户。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/auth/register`
  * **认证要求:** Public
  * **参数说明 (Request Body):**
    | 字段 | 类型 | 是否必须 | 描述 |
    | :--- | :--- | :--- | :--- |
    | `email` | `String` | 是 | 用户邮箱，必须唯一 |
    | `password` | `String` | 是 | 密码 (建议前端加密传输) |
  * **成功响应 (201):**
    ```json
    {
        "code": 201,
        "message": "User registered successfully",
        "data": {
            "user": {
                "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
                "email": "<EMAIL>",
                "is_guest": false
            },
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        }
    }
    ```
  * **失败响应 (409 Conflict):**
    ```json
    {
        "code": 40901,
        "message": "Email already exists",
        "data": null
    }
    ```

### 3\. 用户登录

  * **功能名称:** 用户使用邮箱和密码登录。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/auth/login`
  * **认证要求:** Public
  * **参数说明 (Request Body):**
    | 字段 | 类型 | 是否必须 | 描述 |
    | :--- | :--- | :--- | :--- |
    | `email` | `String` | 是 | 用户邮箱 |
    | `password` | `String` | 是 | 密码 |
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Login successful",
        "data": {
            "user": {
                "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
                "email": "<EMAIL>",
                "is_guest": false
            },
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        }
    }
    ```

-----

## **模块二：AI 角色展示与选择 (AI Role)**

提供所有可用的 AI 角色供用户浏览和选择。

### 1\. 获取 AI 角色列表

  * **功能名称:** 获取所有已启用的 AI 角色列表。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/ai-roles`
  * **认证要求:** Public
  * **参数说明:** 无
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": [
            {
                "id": 1,
                "name": "博学的历史学家",
                "avatar_url": "https://example.com/avatars/historian.png",
                "description": "精通世界历史，能与你深入探讨任何历史事件。"
            },
            {
                "id": 2,
                "name": "温柔的心理顾问",
                "avatar_url": "https://example.com/avatars/counselor.png",
                "description": "倾听你的烦恼，给予你温暖的支持和专业的建议。"
            }
        ]
    }
    ```

### 2\. 获取 AI 角色详情

  * **功能名称:** 获取单个 AI 角色的详细信息。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/ai-roles/{id}`
  * **认证要求:** Public
  * **参数说明 (Path):**
    | 字段 | 类型 | 描述 |
    | :--- | :--- | :--- |
    | `id` | `Integer` | AI 角色的 ID |
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": {
            "id": 1,
            "name": "博学的历史学家",
            "avatar_url": "https://example.com/avatars/historian.png",
            "description": "精通世界历史，能与你深入探讨任何历史事件。",
            "details": "他是一位沉浸在时间长河中的学者，书房里堆满了古籍和地图。他不仅知道重大事件的始末，更了解那些不为人知的历史细节...",
            "system_prompt": "你是一位博学的历史学家..."
        }
    }
    ```
  * **失败响应 (404 Not Found):**
    ```json
    {
        "code": 40401,
        "message": "AI Role not found",
        "data": null
    }
    ```

-----

## **模块三：核心对话功能 (Chat)**

管理聊天会话和消息的核心 API。

### 1\. 创建新的聊天会话

  * **功能名称:** 当用户选择一个 AI 角色并开始聊天时，创建一个新的会话。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/chats`
  * **认证要求:** Authenticated (JWT)
  * **参数说明 (Request Body):**
    | 字段 | 类型 | 是否必须 | 描述 |
    | :--- | :--- | :--- | :--- |
    | `ai_role_id` | `Integer` | 是 | 用户选择的 AI 角色 ID |
  * **成功响应 (201):**
    ```json
    {
        "code": 201,
        "message": "Chat session created successfully",
        "data": {
            "uuid": "c3d4e5f6-a7b8-9012-3456-7890abcdef12",
            "ai_role_id": 1,
            "title": "新的对话", // 默认标题
            "created_at": "2025-07-21T23:23:50.000Z"
        }
    }
    ```

### 2\. 发送消息

  * **功能名称:** 在指定的会话中发送一条消息，并获取 AI 的回复。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/chats/{session_uuid}/messages`
  * **认证要求:** Authenticated (JWT)
  * **参数说明 (Path):**
    | 字段 | 类型 | 描述 |
    | :--- | :--- | :--- |
    | `session_uuid` | `String` | 聊天会话的 `uuid` |
  * **参数说明 (Request Body):**
    | 字段 | 类型 | 是否必须 | 描述 |
    | :--- | :--- | :--- | :--- |
    | `content` | `String` | 是 | 用户发送的消息文本内容 |
  * **成功响应 (201):**
    > **注意:** 响应体中包含了 AI 的回复消息。
    ```json
    {
        "code": 201,
        "message": "Message sent and reply received",
        "data": {
            "role": "assistant",
            "content": "当然，关于古罗马的历史，您想从哪个时期开始了解呢？是共和国时期还是帝国时期？",
            "emotion": null,
            "created_at": "2025-07-21T23:24:00.123Z"
        }
    }
    ```
    > **实现建议:** 后端接收到请求后，需要将用户的消息存入 `chat_messages` 表，然后调用大模型 API 获取回复，再将 AI 的回复存入 `chat_messages` 表，最后将 AI 的回复返回给前端。

### 3\. 获取会话历史消息

  * **功能名称:** 获取指定会话中的所有历史消息。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/chats/{session_uuid}/messages`
  * **认证要求:** Authenticated (JWT)
  * **参数说明 (Path):**
    | 字段 | 类型 | 描述 |
    | :--- | :--- | :--- |
    | `session_uuid` | `String` | 聊天会话的 `uuid` |
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": [
            {
                "role": "user",
                "content": "你好，我想了解一下古罗马的历史。",
                "created_at": "2025-07-21T23:23:55.456Z"
            },
            {
                "role": "assistant",
                "content": "当然，关于古罗马的历史，您想从哪个时期开始了解呢？是共和国时期还是帝国时期？",
                "created_at": "2025-07-21T23:24:00.123Z"
            }
        ]
    }
    ```

-----

## **模块四：用户中心 (User Center)**

提供当前登录用户的个人信息管理和历史记录查询功能。

### 1\. 获取当前用户信息

  * **功能名称:** 获取当前已登录用户的详细信息。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/me`
  * **认证要求:** Authenticated (JWT)
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": {
            "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
            "email": "<EMAIL>",
            "is_guest": false,
            "status": "active",
            "daily_message_count": 5,
            "message_credits": 100,
            "profile_summary": "该用户对古罗马历史表现出浓厚兴趣。",
            "created_at": "2025-07-20T10:00:00.000Z"
        }
    }
    ```

### 2\. 获取用户的所有聊天会话列表

  * **功能名称:** 获取当前用户的所有历史聊天会话列表，按最后更新时间倒序。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/me/chats`
  * **认证要求:** Authenticated (JWT)
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": [
            {
                "uuid": "c3d4e5f6-a7b8-9012-3456-7890abcdef12",
                "title": "关于古罗马历史的探讨", // 标题由第一条消息生成
                "ai_role_id": 1,
                "ai_role_name": "博学的历史学家",
                "updated_at": "2025-07-21T23:24:00.000Z"
            },
            {
                "uuid": "d4e5f6a7-b8c9-0123-4567-890abcdef123",
                "title": "如何应对工作压力",
                "ai_role_id": 2,
                "ai_role_name": "温柔的心理顾问",
                "updated_at": "2025-07-20T18:30:00.000Z"
            }
        ]
    }
    ```

-----

## **模块五：支付与充值 (Payment & Subscription)**

处理商业化功能，包括套餐查询、订单创建和订阅管理。

### 1\. 获取所有可用套餐

  * **功能名称:** 获取所有在售的套餐计划列表。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/plans`
  * **认证要求:** Public (或 Authenticated)
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": [
            {
                "id": 1,
                "name": "月度会员",
                "description": "无限畅聊，解锁所有高级功能。",
                "price": "20.00",
                "plan_type": "subscription",
                "duration_days": 30
            },
            {
                "id": 2,
                "name": "消息加油包",
                "description": "一次性购买500条消息额度。",
                "price": "50.00",
                "plan_type": "one_time",
                "message_credits": 500
            }
        ]
    }
    ```

### 2\. 创建订单

  * **功能名称:** 用户选择一个套餐以创建支付订单。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/orders`
  * **认证要求:** Authenticated (JWT)
  * **参数说明 (Request Body):**
    | 字段 | 类型 | 是否必须 | 描述 |
    | :--- | :--- | :--- | :--- |
    | `plan_id` | `Integer` | 是 | 用户选择的套餐 ID |
  * **成功响应 (201):**
    > **注意:** `payment_details` 的具体内容取决于所集成的支付网关。
    ```json
    {
        "code": 201,
        "message": "Order created successfully",
        "data": {
            "order_uuid": "e5f6a7b8-c9d0-1234-5678-90abcdef1234",
            "status": "pending",
            "amount": "20.00",
            "payment_details": {
                "pay_url": "https://third-party-payment.com/pay?token=xyz"
            }
        }
    }
    ```

### 3\. 支付结果通知 (Webhook)

  * **功能名称:** (供支付平台调用) 接收异步支付结果通知。
  * **HTTP 方法:** `POST`
  * **URL 路径:** `/api/v1/payments/notify`
  * **认证要求:** IP 白名单或签名验证 (由支付平台决定)
  * **参数说明:** 由具体支付平台的回调规范定义。
  * **成功响应 (200):**
    ```
    SUCCESS
    ```
    > **实现建议:** 此接口负责验证回调的合法性，并根据支付结果更新 `orders` 表的状态。如果订单是购买订阅或消息包，则需更新 `user_subscriptions` 表或 `users` 表的 `message_credits` 字段。

### 4\. 获取用户订阅信息

  * **功能名称:** 获取当前用户的有效订阅状态。
  * **HTTP 方法:** `GET`
  * **URL 路径:** `/api/v1/me/subscriptions`
  * **认证要求:** Authenticated (JWT)
  * **成功响应 (200):**
    ```json
    {
        "code": 200,
        "message": "Success",
        "data": [
            {
                "plan_name": "月度会员",
                "start_date": "2025-07-15T00:00:00.000Z",
                "end_date": "2025-08-14T23:59:59.000Z",
                "status": "active"
            }
        ]
    }
    ```

-----

## **模块六：后台管理系统 (Admin)**

此模块提供给运营和管理人员使用，所有接口都应有严格的管理员权限校验。

  * **API 前缀:** `/api/v1/admin`
  * **认证要求:** Authenticated (JWT), 且用户角色必须为 `admin`。

### **用户管理 (User Management)**

  * `GET /api/v1/admin/users`: 获取用户列表 (支持分页、按邮箱/UUID搜索、按状态筛选)。
  * `GET /api/v1/admin/users/{uuid}`: 获取特定用户的详细信息，包括所有会话、订单等。
  * `PUT /api/v1/admin/users/{uuid}`: 更新用户信息，如修改状态 (active/banned)、增减 `message_credits`。

### **AI 角色管理 (AI Role Management)**

  * `POST /api/v1/admin/ai-roles`: 创建新的 AI 角色。
  * `PUT /api/v1/admin/ai-roles/{id}`: 更新指定 AI 角色的信息 (名称、描述、System Prompt等)。
  * `DELETE /api/v1/admin/ai-roles/{id}`: 删除 (软删除，将 `is_active` 设为 `false`) 一个 AI 角色。

### **订单管理 (Order Management)**

  * `GET /api/v1/admin/orders`: 获取订单列表 (支持分页、按用户/订单号/状态筛选)。

### **套餐管理 (Plan Management)**

  * `POST /api/v1/admin/plans`: 创建新的套餐。
  * `PUT /api/v1/admin/plans/{id}`: 更新套餐信息。
  * `DELETE /api/v1/admin/plans/{id}`: 禁用某个套餐 (`is_active` = `false`)。

-----

## **API 使用建议与最佳实践**

1.  **游客转正:** 当游客用户 (`is_guest` = `true`) 选择注册时，前端应调用注册接口。后端可以通过当前游客的 `JWT` 识别其身份，将其 `is_guest` 字段更新为 `false`，并补充 `email` 和 `password_hash`，而不是创建一个全新的用户，以保留其聊天记录。
2.  **每日额度重置:** 用户的每日免费额度 (`daily_message_count`) 需要一个定时任务 (如每天凌晨) 或在用户发消息时检查 `last_usage_date` 来进行重置。
3.  **安全性:** 所有用户输入都必须经过严格的清理和验证，以防止 XSS 和 SQL 注入攻击。密码必须使用强哈希算法 (如 `bcrypt`) 加盐存储。
4.  **性能:** 对于列表接口 (如获取历史消息、获取会话列表)，必须实现分页，避免一次性返回大量数据。利用数据库索引优化查询性能。
5.  **异步处理:** 支付回调等耗时或依赖外部系统的操作，应采用消息队列等异步方式处理，以提高接口响应速度和系统可靠性。  