# AI伙伴聊天应用 - 模块一API文档

## 概述

本文档描述了AI伙伴聊天应用**模块一：用户认证与账户基础**的所有API接口。

- **Base URL**: `http://localhost:3000/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Bearer Token

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 业务数据
  }
}
```

### 错误响应
```json
{
  "code": 40401,
  "message": "资源未找到",
  "data": null
}
```

## 错误码说明

| HTTP状态码 | 业务错误码 | 描述 |
|-----------|-----------|------|
| 200 | 200 | 请求成功 |
| 201 | 201 | 资源创建成功 |
| 400 | 40001 | 请求参数无效 |
| 401 | 40101 | 未授权或Token无效 |
| 401 | 40102 | Token已过期 |
| 403 | 40301 | 禁止访问，权限不足 |
| 404 | 40401 | 请求的资源未找到 |
| 409 | 40901 | 资源冲突，如邮箱已注册 |
| 500 | 50001 | 服务器内部错误 |

---

## 接口列表

### 1. 创建游客账户

**接口描述**: 为首次访问的用户创建临时游客身份

- **URL**: `POST /auth/guest`
- **认证**: 无需认证
- **请求参数**: 无

**响应示例**:
```json
{
  "code": 201,
  "message": "Guest user created successfully",
  "data": {
    "user": {
      "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "is_guest": true
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2. 用户注册

**接口描述**: 使用邮箱和密码注册正式账户

- **URL**: `POST /auth/register`
- **认证**: 无需认证

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**参数说明**:
- `email`: 邮箱地址，必须是有效格式，最大255字符
- `password`: 密码，最少6字符，最多128字符，必须包含字母和数字

**响应示例**:
```json
{
  "code": 201,
  "message": "User registered successfully",
  "data": {
    "user": {
      "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
      "email": "<EMAIL>",
      "is_guest": false
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**错误响应**:
- `409 Conflict`: 邮箱已存在
- `400 Bad Request`: 参数验证失败

### 3. 用户登录

**接口描述**: 使用邮箱和密码登录

- **URL**: `POST /auth/login`
- **认证**: 无需认证

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "user": {
      "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
      "email": "<EMAIL>",
      "is_guest": false
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**错误响应**:
- `401 Unauthorized`: 邮箱或密码错误
- `403 Forbidden`: 账户已被禁用

### 4. 获取当前用户信息

**接口描述**: 获取当前登录用户的详细信息

- **URL**: `GET /auth/me`
- **认证**: 需要JWT Token

**请求头**:
```
Authorization: Bearer <your_jwt_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "uuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
    "email": "<EMAIL>",
    "is_guest": false,
    "status": "active",
    "daily_message_count": 0,
    "message_credits": 0,
    "profile_summary": null,
    "created_at": "2025-07-22T10:00:00.000Z"
  }
}
```

**错误响应**:
- `401 Unauthorized`: Token无效或已过期
- `404 Not Found`: 用户不存在

---

## 认证说明

### JWT Token

- **游客Token**: 有效期1天
- **注册用户Token**: 有效期7天
- **Token格式**: `Bearer <token>`

### 使用示例

```javascript
// 创建游客账户
const guestResponse = await fetch('/api/v1/auth/guest', {
  method: 'POST'
});

// 用户注册
const registerResponse = await fetch('/api/v1/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

// 用户登录
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

// 获取用户信息
const userResponse = await fetch('/api/v1/auth/me', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

---

## 数据库结构

### users表结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 内部主键 |
| uuid | CHAR(36) | 对外公开ID |
| email | VARCHAR(255) | 邮箱（游客为NULL） |
| password_hash | VARCHAR(255) | 加密密码（游客为NULL） |
| is_guest | BOOLEAN | 是否为游客 |
| status | ENUM | 账户状态（active/banned） |
| daily_message_count | INT | 每日消息计数 |
| last_usage_date | DATE | 上次使用日期 |
| message_credits | INT | 剩余消息点数 |
| profile_summary | TEXT | 用户画像摘要 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

---

## 安全特性

1. **密码加密**: 使用bcrypt进行加盐哈希
2. **JWT安全**: 使用强密钥签名
3. **输入验证**: 严格的参数校验
4. **限流保护**: 防止暴力攻击
5. **CORS配置**: 跨域请求控制
6. **安全头**: 使用Helmet中间件
