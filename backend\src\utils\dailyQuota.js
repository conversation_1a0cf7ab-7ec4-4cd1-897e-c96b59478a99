const { query } = require('../config/database');
const { hasActiveSubscription } = require('../services/paymentService');

/**
 * 检查并重置用户的每日额度
 * @param {number} userId - 用户ID
 * @returns {Promise<boolean>} 是否进行了重置
 */
async function checkAndResetDailyQuota(userId) {
  try {
    // 获取用户当前信息
    const users = await query(
      'SELECT daily_message_count, last_usage_date FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      return false;
    }

    const user = users[0];
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
    const lastUsageDate = user.last_usage_date ? user.last_usage_date.toISOString().split('T')[0] : null;

    // 如果是新的一天，重置每日计数
    if (lastUsageDate !== today) {
      await query(
        'UPDATE users SET daily_message_count = 0, last_usage_date = CURRENT_DATE WHERE id = ?',
        [userId]
      );
      return true;
    }

    return false;
  } catch (error) {
    console.error('检查每日额度失败:', error);
    return false;
  }
}

/**
 * 增加用户的每日消息计数
 * @param {number} userId - 用户ID
 * @param {number} increment - 增加的数量，默认为1
 * @returns {Promise<number>} 更新后的计数
 */
async function incrementDailyMessageCount(userId, increment = 1) {
  try {
    // 🔧 修复：不在这里重置额度，避免重复重置
    // checkAndResetDailyQuota 应该只在 consumeQuota 开始时调用一次

    // 增加计数
    await query(
      'UPDATE users SET daily_message_count = daily_message_count + ?, last_usage_date = CURRENT_DATE WHERE id = ?',
      [increment, userId]
    );

    // 获取更新后的计数
    const users = await query(
      'SELECT daily_message_count FROM users WHERE id = ?',
      [userId]
    );

    return users.length > 0 ? users[0].daily_message_count : 0;
  } catch (error) {
    console.error('增加每日消息计数失败:', error);
    throw error;
  }
}

/**
 * 获取用户的每日额度信息（支持VIP无限额度）
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 额度信息
 */
async function getDailyQuotaInfo(userId) {
  try {
    // 🔧 修复：不在查询时重置额度，避免重复重置

    // 获取用户信息
    const users = await query(
      'SELECT daily_message_count, message_credits, is_guest FROM users WHERE id = ?',
      [userId]
    );

    if (users.length === 0) {
      throw new Error('USER_NOT_FOUND');
    }

    const user = users[0];

    // 🆕 检查用户是否为VIP会员
    const isVip = await hasActiveSubscription(userId);

    // 🆕 根据用户类型设置每日额度
    let dailyLimit;
    if (isVip) {
      // VIP会员：无限额度（设置为一个很大的数字）
      dailyLimit = parseInt(process.env.VIP_DAILY_LIMIT || '999999');
    } else if (user.is_guest) {
      // 游客用户
      dailyLimit = parseInt(process.env.GUEST_DAILY_LIMIT || '10');
    } else {
      // 普通注册用户
      dailyLimit = parseInt(process.env.USER_DAILY_LIMIT || '50');
    }

    return {
      daily_message_count: user.daily_message_count,
      daily_limit: dailyLimit,
      remaining_daily: Math.max(0, dailyLimit - user.daily_message_count),
      message_credits: user.message_credits || 0,
      is_guest: Boolean(user.is_guest),
      is_vip: isVip  // 🆕 返回VIP状态
    };
  } catch (error) {
    console.error('获取每日额度信息失败:', error);
    throw error;
  }
}

/**
 * 检查用户是否还有可用额度
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 额度检查结果
 */
async function checkQuotaAvailable(userId) {
  try {
    const quotaInfo = await getDailyQuotaInfo(userId);
    
    // 如果有消息点数，优先使用点数
    if (quotaInfo.message_credits > 0) {
      return {
        available: true,
        type: 'credits',
        remaining: quotaInfo.message_credits
      };
    }

    // 检查每日免费额度
    if (quotaInfo.remaining_daily > 0) {
      return {
        available: true,
        type: 'daily',
        remaining: quotaInfo.remaining_daily
      };
    }

    return {
      available: false,
      type: 'none',
      remaining: 0
    };
  } catch (error) {
    console.error('检查额度可用性失败:', error);
    throw error;
  }
}

/**
 * 消费用户额度（优先使用点数，再使用每日免费额度）
 * @param {number} userId - 用户ID
 * @param {number} cost - 消费数量，默认为1
 * @returns {Promise<Object>} 消费结果
 */
async function consumeQuota(userId, cost = 1) {
  try {
    // 🔧 修复：在消费前检查并重置每日额度
    await checkAndResetDailyQuota(userId);

    const quotaInfo = await getDailyQuotaInfo(userId);
    
    // 优先使用消息点数
    if (quotaInfo.message_credits >= cost) {
      await query(
        'UPDATE users SET message_credits = message_credits - ? WHERE id = ?',
        [cost, userId]
      );
      
      return {
        success: true,
        type: 'credits',
        consumed: cost,
        remaining_credits: quotaInfo.message_credits - cost,
        remaining_daily: quotaInfo.remaining_daily
      };
    }

    // 使用每日免费额度
    if (quotaInfo.remaining_daily >= cost) {
      const newCount = await incrementDailyMessageCount(userId, cost);
      
      return {
        success: true,
        type: 'daily',
        consumed: cost,
        remaining_credits: quotaInfo.message_credits,
        remaining_daily: Math.max(0, quotaInfo.daily_limit - newCount)
      };
    }

    // 额度不足
    return {
      success: false,
      type: 'insufficient',
      consumed: 0,
      remaining_credits: quotaInfo.message_credits,
      remaining_daily: quotaInfo.remaining_daily
    };
  } catch (error) {
    console.error('消费额度失败:', error);
    throw error;
  }
}

/**
 * 批量重置所有用户的每日额度（定时任务使用）
 * @returns {Promise<number>} 重置的用户数量
 */
async function batchResetDailyQuota() {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    // 重置所有非今日使用的用户的每日计数
    const result = await query(
      'UPDATE users SET daily_message_count = 0, last_usage_date = CURRENT_DATE WHERE last_usage_date < ? OR last_usage_date IS NULL',
      [today]
    );

    console.log(`每日额度重置完成，影响用户数: ${result.affectedRows}`);
    return result.affectedRows;
  } catch (error) {
    console.error('批量重置每日额度失败:', error);
    throw error;
  }
}

module.exports = {
  checkAndResetDailyQuota,
  incrementDailyMessageCount,
  getDailyQuotaInfo,
  checkQuotaAvailable,
  consumeQuota,
  batchResetDailyQuota
};
