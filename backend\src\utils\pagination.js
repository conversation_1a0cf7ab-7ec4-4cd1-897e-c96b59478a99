/**
 * 分页工具类
 */
class Pagination {
  constructor(page = 1, limit = 20, maxLimit = 100) {
    this.page = Math.max(1, parseInt(page) || 1);
    this.limit = Math.min(maxLimit, Math.max(1, parseInt(limit) || 20));
    this.offset = (this.page - 1) * this.limit;
  }

  /**
   * 获取SQL LIMIT和OFFSET子句
   * @returns {string} SQL子句
   */
  getSqlClause() {
    return `LIMIT ${this.limit} OFFSET ${this.offset}`;
  }

  /**
   * 获取分页参数对象
   * @returns {Object} 分页参数
   */
  getParams() {
    return {
      page: this.page,
      limit: this.limit,
      offset: this.offset
    };
  }

  /**
   * 计算分页元数据
   * @param {number} totalCount - 总记录数
   * @returns {Object} 分页元数据
   */
  getMetadata(totalCount) {
    const totalPages = Math.ceil(totalCount / this.limit);
    const hasNext = this.page < totalPages;
    const hasPrev = this.page > 1;

    return {
      page: this.page,
      limit: this.limit,
      total: totalCount,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? this.page + 1 : null,
      prevPage: hasPrev ? this.page - 1 : null
    };
  }

  /**
   * 格式化分页响应
   * @param {Array} data - 数据数组
   * @param {number} totalCount - 总记录数
   * @returns {Object} 格式化的响应
   */
  formatResponse(data, totalCount) {
    return {
      data,
      pagination: this.getMetadata(totalCount)
    };
  }
}

/**
 * 创建分页实例
 * @param {Object} query - 查询参数
 * @param {Object} options - 选项
 * @returns {Pagination} 分页实例
 */
function createPagination(query = {}, options = {}) {
  const { maxLimit = 100, defaultLimit = 20 } = options;
  const { page, limit } = query;

  return new Pagination(page, limit || defaultLimit, maxLimit);
}

/**
 * 执行分页查询
 * @param {Function} queryFn - 查询函数
 * @param {Function} countFn - 计数函数
 * @param {Pagination} pagination - 分页实例
 * @returns {Object} 分页结果
 */
async function executePaginatedQuery(queryFn, countFn, pagination) {
  try {
    // 并行执行数据查询和计数查询
    const [data, countResult] = await Promise.all([
      queryFn(pagination),
      countFn()
    ]);

    const totalCount = Array.isArray(countResult) ? countResult[0]?.count || 0 : countResult;

    return pagination.formatResponse(data, totalCount);
  } catch (error) {
    console.error('分页查询执行失败:', error);
    throw error;
  }
}

/**
 * 聊天消息分页查询
 * @param {string} sessionId - 会话ID
 * @param {Object} query - 查询参数
 * @param {Function} dbQuery - 数据库查询函数
 * @returns {Object} 分页结果
 */
async function paginateMessages(sessionId, query, dbQuery) {
  const pagination = createPagination(query, { maxLimit: 50, defaultLimit: 20 });

  const queryFn = async (pg) => {
    return await dbQuery(
      `SELECT role, content, emotion, created_at 
       FROM chat_messages 
       WHERE session_id = ? 
       ORDER BY created_at ASC 
       ${pg.getSqlClause()}`,
      [sessionId]
    );
  };

  const countFn = async () => {
    const result = await dbQuery(
      'SELECT COUNT(*) as count FROM chat_messages WHERE session_id = ?',
      [sessionId]
    );
    return result[0].count;
  };

  return await executePaginatedQuery(queryFn, countFn, pagination);
}

/**
 * 用户会话分页查询
 * @param {number} userId - 用户ID
 * @param {Object} query - 查询参数
 * @param {Function} dbQuery - 数据库查询函数
 * @returns {Object} 分页结果
 */
async function paginateSessions(userId, query, dbQuery) {
  const pagination = createPagination(query, { maxLimit: 50, defaultLimit: 10 });

  const queryFn = async (pg) => {
    return await dbQuery(
      `SELECT cs.uuid, cs.title, cs.created_at, cs.updated_at,
              ar.id as ai_role_id, ar.name as ai_role_name
       FROM chat_sessions cs
       JOIN ai_roles ar ON cs.ai_role_id = ar.id
       WHERE cs.user_id = ?
       ORDER BY cs.updated_at DESC
       ${pg.getSqlClause()}`,
      [userId]
    );
  };

  const countFn = async () => {
    const result = await dbQuery(
      'SELECT COUNT(*) as count FROM chat_sessions WHERE user_id = ?',
      [userId]
    );
    return result[0].count;
  };

  return await executePaginatedQuery(queryFn, countFn, pagination);
}

/**
 * 管理员订单分页查询
 * @param {Object} filters - 筛选条件
 * @param {Object} query - 查询参数
 * @param {Function} dbQuery - 数据库查询函数
 * @returns {Object} 分页结果
 */
async function paginateOrders(filters, query, dbQuery) {
  const pagination = createPagination(query, { maxLimit: 100, defaultLimit: 20 });

  // 构建WHERE条件
  const conditions = [];
  const params = [];

  if (filters.status) {
    conditions.push('o.status = ?');
    params.push(filters.status);
  }

  if (filters.user_search) {
    conditions.push('(u.email LIKE ? OR u.uuid LIKE ?)');
    const searchTerm = `%${filters.user_search}%`;
    params.push(searchTerm, searchTerm);
  }

  if (filters.order_search) {
    conditions.push('o.order_number LIKE ?');
    params.push(`%${filters.order_search}%`);
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  const queryFn = async (pg) => {
    return await dbQuery(
      `SELECT o.id, o.order_number, o.amount, o.status, o.created_at,
              u.uuid as user_uuid, u.email as user_email,
              p.name as plan_name, p.plan_type
       FROM orders o
       JOIN users u ON o.user_id = u.id
       JOIN plans p ON o.plan_id = p.id
       ${whereClause}
       ORDER BY o.created_at DESC
       ${pg.getSqlClause()}`,
      [...params]
    );
  };

  const countFn = async () => {
    const result = await dbQuery(
      `SELECT COUNT(*) as count
       FROM orders o
       JOIN users u ON o.user_id = u.id
       JOIN plans p ON o.plan_id = p.id
       ${whereClause}`,
      [...params]
    );
    return result[0].count;
  };

  return await executePaginatedQuery(queryFn, countFn, pagination);
}

/**
 * 管理员用户分页查询
 * @param {Object} filters - 筛选条件
 * @param {Object} query - 查询参数
 * @param {Function} dbQuery - 数据库查询函数
 * @returns {Object} 分页结果
 */
async function paginateUsers(filters, query, dbQuery) {
  const pagination = createPagination(query, { maxLimit: 100, defaultLimit: 20 });

  // 构建WHERE条件
  const conditions = [];
  const params = [];

  if (filters.status) {
    conditions.push('status = ?');
    params.push(filters.status);
  }

  if (filters.is_guest !== undefined) {
    conditions.push('is_guest = ?');
    params.push(filters.is_guest);
  }

  if (filters.search) {
    conditions.push('(email LIKE ? OR uuid LIKE ?)');
    const searchTerm = `%${filters.search}%`;
    params.push(searchTerm, searchTerm);
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  const queryFn = async (pg) => {
    return await dbQuery(
      `SELECT uuid, email, is_guest, status, daily_message_count, 
              message_credits, created_at, updated_at
       FROM users
       ${whereClause}
       ORDER BY created_at DESC
       ${pg.getSqlClause()}`,
      [...params]
    );
  };

  const countFn = async () => {
    const result = await dbQuery(
      `SELECT COUNT(*) as count FROM users ${whereClause}`,
      [...params]
    );
    return result[0].count;
  };

  return await executePaginatedQuery(queryFn, countFn, pagination);
}

module.exports = {
  Pagination,
  createPagination,
  executePaginatedQuery,
  paginateMessages,
  paginateSessions,
  paginateOrders,
  paginateUsers
};
