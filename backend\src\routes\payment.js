const express = require('express');
const paymentController = require('../controllers/paymentController');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { validate, createOrderSchema, paymentNotifySchema } = require('../validators/payment');

const router = express.Router();

/**
 * @route GET /api/v1/plans
 * @desc 获取所有可用套餐
 * @access Public
 */
router.get('/plans', paymentController.getPlans);

/**
 * @route POST /api/v1/orders
 * @desc 创建订单
 * @access Private
 */
router.post('/orders', authenticateToken, validate(createOrderSchema), paymentController.createOrder);

/**
 * @route POST /api/v1/payments/notify
 * @desc 支付结果通知（模拟）
 * @access Public (实际应用中需要IP白名单或签名验证)
 */
router.post('/payments/notify', validate(paymentNotifySchema), paymentController.paymentNotify);

/**
 * @route GET /api/v1/me/subscriptions
 * @desc 获取用户订阅信息
 * @access Private
 */
router.get('/me/subscriptions', authenticateToken, paymentController.getUserSubscriptions);

/**
 * @route GET /api/v1/me/orders
 * @desc 获取用户订单历史
 * @access Private
 */
router.get('/me/orders', authenticateToken, paymentController.getUserOrders);

module.exports = router;
