const { query } = require('../config/database');

/**
 * 获取所有启用的AI角色列表
 */
async function getAllActiveAiRoles() {
  const roles = await query(
    'SELECT id, name, avatar_url, description FROM ai_roles WHERE is_active = ? ORDER BY id ASC',
    [true]
  );

  return roles;
}

/**
 * 根据ID获取AI角色详情
 */
async function getAiRoleById(id) {
  const roles = await query(
    'SELECT id, name, avatar_url, description, details, system_prompt, created_at, updated_at FROM ai_roles WHERE id = ? AND is_active = ?',
    [id, true]
  );

  return roles.length > 0 ? roles[0] : null;
}

/**
 * 检查AI角色是否存在且启用
 */
async function checkAiRoleExists(id) {
  const roles = await query(
    'SELECT id FROM ai_roles WHERE id = ? AND is_active = ?',
    [id, true]
  );

  return roles.length > 0;
}

module.exports = {
  getAllActiveAiRoles,
  getAiRoleById,
  checkAiRoleExists
};
