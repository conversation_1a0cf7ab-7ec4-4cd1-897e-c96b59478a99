const request = require('supertest');
const app = require('../src/app');

describe('支付模块测试', () => {
  let userToken;
  let guestToken;
  let validPlanId;
  let orderUuid;
  const testEmail = `payment${Date.now()}@example.com`;
  const testPassword = 'test123456';

  beforeAll(async () => {
    // 创建测试用户并获取token
    const registerResponse = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: testEmail,
        password: testPassword
      });

    userToken = registerResponse.body.data.token;

    // 创建游客用户
    const guestResponse = await request(app)
      .post('/api/v1/auth/guest');

    guestToken = guestResponse.body.data.token;

    // 获取一个有效的套餐ID
    const plansResponse = await request(app)
      .get('/api/v1/plans');

    if (plansResponse.body.data.length > 0) {
      validPlanId = plansResponse.body.data[0].id;
    }
  });

  describe('GET /api/v1/plans', () => {
    test('应该成功获取套餐列表', async () => {
      const response = await request(app)
        .get('/api/v1/plans')
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(Array.isArray(response.body.data)).toBe(true);
      
      // 检查返回的数据结构
      if (response.body.data.length > 0) {
        const plan = response.body.data[0];
        expect(plan).toHaveProperty('id');
        expect(plan).toHaveProperty('name');
        expect(plan).toHaveProperty('description');
        expect(plan).toHaveProperty('price');
        expect(plan).toHaveProperty('plan_type');
        expect(['subscription', 'one_time']).toContain(plan.plan_type);
        
        if (plan.plan_type === 'subscription') {
          expect(plan).toHaveProperty('duration_days');
        } else {
          expect(plan).toHaveProperty('message_credits');
        }
      }
    });

    test('应该返回按ID升序排列的套餐列表', async () => {
      const response = await request(app)
        .get('/api/v1/plans')
        .expect(200);

      const plans = response.body.data;
      if (plans.length > 1) {
        for (let i = 1; i < plans.length; i++) {
          expect(plans[i].id).toBeGreaterThan(plans[i-1].id);
        }
      }
    });
  });

  describe('POST /api/v1/orders', () => {
    test('应该成功创建订单', async () => {
      if (!validPlanId) {
        console.log('跳过测试：没有可用的套餐');
        return;
      }

      const response = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          plan_id: validPlanId
        })
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.message).toBe('Order created successfully');
      expect(response.body.data).toHaveProperty('order_uuid');
      expect(response.body.data).toHaveProperty('status', 'pending');
      expect(response.body.data).toHaveProperty('amount');
      expect(response.body.data).toHaveProperty('payment_details');
      expect(response.body.data.payment_details).toHaveProperty('pay_url');

      orderUuid = response.body.data.order_uuid;
    });

    test('游客用户也应该能创建订单', async () => {
      if (!validPlanId) {
        console.log('跳过测试：没有可用的套餐');
        return;
      }

      const response = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${guestToken}`)
        .send({
          plan_id: validPlanId
        })
        .expect(200);

      expect(response.body.code).toBe(201);
      expect(response.body.data).toHaveProperty('order_uuid');
    });

    test('应该拒绝不存在的套餐ID', async () => {
      const response = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          plan_id: 99999
        })
        .expect(404);

      expect(response.body.code).toBe(40401);
      expect(response.body.message).toBe('套餐不存在或已禁用');
    });

    test('应该拒绝无效的套餐ID格式', async () => {
      const response = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          plan_id: 'invalid'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .post('/api/v1/orders')
        .send({
          plan_id: validPlanId
        })
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('POST /api/v1/payments/notify', () => {
    test('应该成功处理支付成功通知', async () => {
      if (!orderUuid) {
        console.log('跳过测试：没有可用的订单');
        return;
      }

      const response = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: orderUuid,
          status: 'success',
          transaction_id: 'mock_txn_' + Date.now(),
          gateway: 'mock_payment'
        })
        .expect(200);

      expect(response.text).toBe('SUCCESS');
    });

    test('应该成功处理支付失败通知', async () => {
      // 创建一个新订单用于测试失败场景
      if (!validPlanId) {
        console.log('跳过测试：没有可用的套餐');
        return;
      }

      const orderResponse = await request(app)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          plan_id: validPlanId
        });

      const failOrderUuid = orderResponse.body.data.order_uuid;

      const response = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: failOrderUuid,
          status: 'failed',
          transaction_id: 'mock_fail_txn_' + Date.now(),
          gateway: 'mock_payment'
        })
        .expect(200);

      expect(response.text).toBe('SUCCESS');
    });

    test('应该拒绝不存在的订单', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      
      const response = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: fakeUuid,
          status: 'success',
          transaction_id: 'mock_txn_123'
        })
        .expect(404);

      expect(response.text).toBe('ORDER_NOT_FOUND');
    });

    test('应该拒绝重复处理的订单', async () => {
      if (!orderUuid) {
        console.log('跳过测试：没有可用的订单');
        return;
      }

      // 尝试再次处理已经处理过的订单
      const response = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: orderUuid,
          status: 'success',
          transaction_id: 'mock_txn_duplicate'
        })
        .expect(400);

      expect(response.text).toBe('ORDER_ALREADY_PROCESSED');
    });

    test('应该拒绝无效的订单UUID格式', async () => {
      const response = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: 'invalid-uuid',
          status: 'success'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });

    test('应该拒绝无效的支付状态', async () => {
      const fakeUuid = '12345678-1234-1234-1234-123456789012';
      
      const response = await request(app)
        .post('/api/v1/payments/notify')
        .send({
          order_uuid: fakeUuid,
          status: 'invalid_status'
        })
        .expect(400);

      expect(response.body.code).toBe(40001);
    });
  });

  describe('GET /api/v1/me/subscriptions', () => {
    test('应该成功获取用户订阅信息', async () => {
      const response = await request(app)
        .get('/api/v1/me/subscriptions')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(Array.isArray(response.body.data)).toBe(true);

      // 如果有订阅，检查数据结构
      if (response.body.data.length > 0) {
        const subscription = response.body.data[0];
        expect(subscription).toHaveProperty('plan_name');
        expect(subscription).toHaveProperty('start_date');
        expect(subscription).toHaveProperty('end_date');
        expect(subscription).toHaveProperty('status');
      }
    });

    test('游客用户也应该能获取订阅信息', async () => {
      const response = await request(app)
        .get('/api/v1/me/subscriptions')
        .set('Authorization', `Bearer ${guestToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .get('/api/v1/me/subscriptions')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });

  describe('GET /api/v1/me/orders', () => {
    test('应该成功获取用户订单历史', async () => {
      const response = await request(app)
        .get('/api/v1/me/orders')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(Array.isArray(response.body.data)).toBe(true);

      // 应该至少有一个订单（之前创建的）
      expect(response.body.data.length).toBeGreaterThan(0);

      const order = response.body.data[0];
      expect(order).toHaveProperty('uuid');
      expect(order).toHaveProperty('amount');
      expect(order).toHaveProperty('status');
      expect(order).toHaveProperty('plan_name');
      expect(order).toHaveProperty('created_at');
    });

    test('应该拒绝缺少认证token', async () => {
      const response = await request(app)
        .get('/api/v1/me/orders')
        .expect(401);

      expect(response.body.code).toBe(40101);
    });
  });
});
