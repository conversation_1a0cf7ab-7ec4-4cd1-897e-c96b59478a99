const express = require('express');
const adminController = require('../controllers/adminController');
const { requireAdmin } = require('../middleware/auth');
const { 
  validate, 
  validateParams, 
  validateQuery,
  updateUserStatusSchema, 
  userUuidSchema,
  paginationSchema 
} = require('../validators/admin');

const router = express.Router();

// 所有管理员接口都需要管理员权限
router.use(requireAdmin);

/**
 * @route GET /api/v1/admin/stats
 * @desc 获取系统统计信息
 * @access Admin
 */
router.get('/stats', adminController.getSystemStats);

/**
 * @route GET /api/v1/admin/users
 * @desc 获取用户列表（分页）
 * @access Admin
 */
router.get('/users', validateQuery(paginationSchema), adminController.getUserList);

/**
 * @route GET /api/v1/admin/users/:user_uuid
 * @desc 获取用户详情
 * @access Admin
 */
router.get('/users/:user_uuid', validateParams(userUuidSchema), adminController.getUserDetail);

/**
 * @route PUT /api/v1/admin/users/:user_uuid/status
 * @desc 更新用户状态
 * @access Admin
 */
router.put('/users/:user_uuid/status', 
  validateParams(userUuidSchema), 
  validate(updateUserStatusSchema), 
  adminController.updateUserStatus
);

/**
 * @route GET /api/v1/admin/users/:user_uuid/chats
 * @desc 获取用户的聊天会话列表
 * @access Admin
 */
router.get('/users/:user_uuid/chats', validateParams(userUuidSchema), adminController.getUserChatSessions);

/**
 * @route GET /api/v1/admin/users/:user_uuid/orders
 * @desc 获取用户的订单列表
 * @access Admin
 */
router.get('/users/:user_uuid/orders', validateParams(userUuidSchema), adminController.getUserOrders);

module.exports = router;
