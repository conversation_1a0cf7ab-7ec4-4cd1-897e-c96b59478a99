const express = require('express');
const adminController = require('../controllers/adminController');
const { requireAdmin } = require('../middleware/auth');
const {
  validate,
  validateParams,
  validateQuery,
  updateUserStatusSchema,
  userUuidSchema,
  paginationSchema,
  createAiRoleSchema,
  updateAiRoleSchema,
  aiRoleIdSchema,
  orderQuerySchema
} = require('../validators/admin');

const router = express.Router();

// 所有管理员接口都需要管理员权限
router.use(requireAdmin);

/**
 * @route GET /api/v1/admin/stats
 * @desc 获取系统统计信息
 * @access Admin
 */
router.get('/stats', adminController.getSystemStats);

/**
 * @route GET /api/v1/admin/users
 * @desc 获取用户列表（分页）
 * @access Admin
 */
router.get('/users', validateQuery(paginationSchema), adminController.getUserList);

/**
 * @route GET /api/v1/admin/users/:user_uuid
 * @desc 获取用户详情
 * @access Admin
 */
router.get('/users/:user_uuid', validateParams(userUuidSchema), adminController.getUserDetail);

/**
 * @route PUT /api/v1/admin/users/:user_uuid/status
 * @desc 更新用户状态
 * @access Admin
 */
router.put('/users/:user_uuid/status', 
  validateParams(userUuidSchema), 
  validate(updateUserStatusSchema), 
  adminController.updateUserStatus
);

/**
 * @route GET /api/v1/admin/users/:user_uuid/chats
 * @desc 获取用户的聊天会话列表
 * @access Admin
 */
router.get('/users/:user_uuid/chats', validateParams(userUuidSchema), adminController.getUserChatSessions);

/**
 * @route GET /api/v1/admin/users/:user_uuid/orders
 * @desc 获取用户的订单列表
 * @access Admin
 */
router.get('/users/:user_uuid/orders', validateParams(userUuidSchema), adminController.getUserOrders);

/**
 * @route POST /api/v1/admin/ai-roles
 * @desc 创建新的AI角色
 * @access Admin
 */
router.post('/ai-roles', validate(createAiRoleSchema), adminController.createAiRole);

/**
 * @route PUT /api/v1/admin/ai-roles/:id
 * @desc 更新指定AI角色的信息
 * @access Admin
 */
router.put('/ai-roles/:id', validateParams(aiRoleIdSchema), validate(updateAiRoleSchema), adminController.updateAiRole);

/**
 * @route DELETE /api/v1/admin/ai-roles/:id
 * @desc 删除AI角色（软删除）
 * @access Admin
 */
router.delete('/ai-roles/:id', validateParams(aiRoleIdSchema), adminController.deleteAiRole);

/**
 * @route GET /api/v1/admin/orders
 * @desc 获取订单列表（分页、筛选）
 * @access Admin
 */
router.get('/orders', validateQuery(orderQuerySchema), adminController.getOrderList);

module.exports = router;
